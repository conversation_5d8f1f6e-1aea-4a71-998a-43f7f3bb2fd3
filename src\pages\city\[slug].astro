---
import Layout from '../../layouts/Layout.astro';
import { getCityBySlug, getListingsForCity, getCategories } from '../../lib/database';

// Get the city slug from the URL
const { slug } = Astro.params;

console.log('🏙️ City page - slug:', slug, 'URL:', Astro.url.toString());

if (!slug) {
  console.log('❌ No slug provided, redirecting to home');
  return Astro.redirect('/');
}

// Get city data from database
console.log('🔍 Looking up city:', slug);
const { data: city, error: cityError } = await getCityBySlug(slug);

if (cityError || !city) {
  console.error('❌ City not found:', slug, cityError);
  return Astro.redirect('/');
}

console.log('✅ City found:', city.name, 'ID:', city.id);

// Get listings for this city
const { data: listings, error: listingsError } = await getListingsForCity(city.id);

// Get categories
const { data: categories, error: categoriesError } = await getCategories();

const pageTitle = `Find Local Businesses in ${city.name}`;
const pageDescription = `Discover trusted local businesses and services in ${city.name}. From housing and healthcare to dining and entertainment - find everything you need as an expat.`;
---

<Layout title={pageTitle} description={pageDescription} city={city.name}>
  <main class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{city.name}</h1>
            <p class="text-gray-600 mt-1">Find local businesses and services</p>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-right text-sm text-gray-500">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </div>
            <a href="https://expatslist.org" class="text-blue-600 hover:underline">← All cities</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Categories Section -->
    {categories && categories.length > 0 && (
      <div class="max-w-7xl mx-auto px-4 py-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Browse Categories</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {categories.map((category) => (
            <div class="bg-white hover:bg-blue-50 p-4 rounded-lg border border-gray-200 transition-all duration-300 cursor-pointer hover:shadow-md">
              <div class="text-center">
                <div class="text-3xl mb-2">{category.icon_slug || '📁'}</div>
                <h3 class="text-sm font-medium text-gray-900 leading-tight">
                  {category.name}
                </h3>
              </div>
            </div>
          ))}
        </div>
      </div>
    )}

    <!-- Recent Listings Section -->
    {listings && listings.length > 0 && (
      <div class="max-w-7xl mx-auto px-4 pb-8">
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">
            Local Businesses ({listings.length})
          </h2>
          <div class="space-y-4">
            {listings.map((listing) => (
              <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900">
                    <a href={`/listing/${listing.slug}`} class="hover:text-blue-600">
                      {listing.display_name || listing.business_name}
                    </a>
                  </h3>
                  <p class="text-sm text-gray-600">{listing.category_name}</p>
                  {listing.short_description && (
                    <p class="text-sm text-gray-500 mt-1">{listing.short_description}</p>
                  )}
                </div>
                <div class="text-right">
                  {listing.is_verified_expatslist && (
                    <span class="inline-block px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                      Verified
                    </span>
                  )}
                  {listing.is_pinned && (
                    <span class="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded ml-1">
                      Featured
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )}

    <!-- Empty State -->
    {(!listings || listings.length === 0) && (
      <div class="max-w-7xl mx-auto px-4 pb-8">
        <div class="bg-white rounded-lg border border-gray-200 p-8 text-center">
          <h2 class="text-xl font-semibold text-gray-900 mb-2">No listings yet</h2>
          <p class="text-gray-600 mb-4">Be the first to add a business in {city.name}!</p>
          <a
            href="/list-business"
            class="inline-block bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Add Your Business
          </a>
        </div>
      </div>
    )}
  </main>
</Layout>
