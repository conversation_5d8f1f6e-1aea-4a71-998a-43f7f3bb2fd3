---
// Path-based city page: /cities/playa-del-carmen/
import Layout from '../../../layouts/Layout.astro';
import { getCityBySlug, getListingsForCity, getCategories } from '../../../lib/database';

const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(slug);

if (cityError || !city) {
  return Astro.redirect('/');
}

// Get listings and categories
const { data: listings } = await getListingsForCity(city.id);
const { data: categories } = await getCategories();

const pageTitle = `${city.name} - Local Businesses for Expats`;
const pageDescription = `Discover trusted local businesses in ${city.name}. Find restaurants, services, and more recommended by fellow expats.`;
---

<Layout title={pageTitle} description={pageDescription}>
  <main class="min-h-screen bg-gray-50">
    <!-- Breadcrumb -->
    <div class="bg-white border-b">
      <div class="max-w-7xl mx-auto px-4 py-3">
        <nav class="text-sm">
          <a href="/" class="text-blue-600 hover:underline">Home</a>
          <span class="mx-2 text-gray-400">›</span>
          <a href="/cities" class="text-blue-600 hover:underline">Cities</a>
          <span class="mx-2 text-gray-400">›</span>
          <span class="text-gray-900">{city.name}</span>
        </nav>
      </div>
    </div>

    <!-- City Header -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50">
      <div class="max-w-7xl mx-auto px-4 py-12">
        <div class="text-center">
          <h1 class="text-4xl font-bold text-gray-900 mb-4">
            {city.name}
          </h1>
          <p class="text-xl text-gray-600 mb-8">
            Find trusted local businesses recommended by expats
          </p>
          
          <!-- Quick Search -->
          <div class="max-w-md mx-auto">
            <form action={`/cities/${slug}/search`} method="GET" class="flex">
              <input
                type="text"
                name="q"
                placeholder="Search businesses..."
                class="flex-1 px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="submit"
                class="px-6 py-3 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors"
              >
                Search
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Categories Grid -->
    {categories && categories.length > 0 && (
      <div class="max-w-7xl mx-auto px-4 py-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">Browse Categories</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {categories.map((category) => (
            <a
              href={`/cities/${slug}/category/${category.id}`}
              class="bg-white hover:bg-blue-50 p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:shadow-md group"
            >
              <div class="text-center">
                <div class="text-3xl mb-3 group-hover:scale-110 transition-transform">
                  {category.icon_slug || '📁'}
                </div>
                <h3 class="text-sm font-medium text-gray-900 leading-tight">
                  {category.name}
                </h3>
              </div>
            </a>
          ))}
        </div>
      </div>
    )}

    <!-- Recent Listings -->
    {listings && listings.length > 0 && (
      <div class="max-w-7xl mx-auto px-4 pb-12">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-2xl font-bold text-gray-900">
              Featured Businesses ({listings.length})
            </h2>
            <a
              href={`/cities/${slug}/all`}
              class="text-blue-600 hover:text-blue-700 font-medium"
            >
              View all →
            </a>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {listings.slice(0, 6).map((listing) => (
              <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div class="flex items-start justify-between mb-3">
                  <h3 class="font-semibold text-gray-900 text-lg">
                    <a
                      href={`/cities/${slug}/listing/${listing.slug}`}
                      class="hover:text-blue-600 transition-colors"
                    >
                      {listing.display_name || listing.business_name}
                    </a>
                  </h3>
                  <div class="flex gap-1">
                    {listing.is_verified_expatslist && (
                      <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                        ✓ Verified
                      </span>
                    )}
                    {listing.is_pinned && (
                      <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                        📌 Featured
                      </span>
                    )}
                  </div>
                </div>
                
                <p class="text-sm text-gray-600 mb-3">{listing.category_name}</p>
                
                {listing.description_short && (
                  <p class="text-sm text-gray-700 mb-4 line-clamp-2">
                    {listing.description_short}
                  </p>
                )}
                
                <div class="flex items-center justify-between">
                  <div class="flex items-center text-sm text-gray-500">
                    {listing.languages_spoken && listing.languages_spoken.includes('English') && (
                      <span class="mr-3">🇺🇸 English</span>
                    )}
                    {listing.expat_friendly_rating && (
                      <span>⭐ {listing.expat_friendly_rating}/5</span>
                    )}
                  </div>
                  
                  <a
                    href={`/cities/${slug}/listing/${listing.slug}`}
                    class="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    View details →
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )}

    <!-- Empty State -->
    {(!listings || listings.length === 0) && (
      <div class="max-w-7xl mx-auto px-4 pb-12">
        <div class="bg-white rounded-xl border border-gray-200 p-12 text-center">
          <div class="text-6xl mb-4">🏪</div>
          <h2 class="text-2xl font-bold text-gray-900 mb-4">No businesses yet</h2>
          <p class="text-gray-600 mb-8 max-w-md mx-auto">
            Be the first to add a business in {city.name} and help fellow expats discover great local services.
          </p>
          <a
            href="/list-business"
            class="inline-block bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Add Your Business
          </a>
        </div>
      </div>
    )}
  </main>
</Layout>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
