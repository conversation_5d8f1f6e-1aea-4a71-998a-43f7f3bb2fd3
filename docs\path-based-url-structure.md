# Path-Based URL Structure for ExpatsList

## Current (Subdomain) vs Proposed (Path-Based)

### Current Structure:
```
Main site:     expatslist.org
City:          playadelcarmen.expatslist.org/
Category:      playadelcarmen.expatslist.org/category/restaurants
Listing:       playadelcarmen.expatslist.org/listing/business-name
Search:        playadelcarmen.expatslist.org/search?q=coffee
```

### Proposed Structure:
```
Main site:     expatslist.org
Cities index:  expatslist.org/cities
City:          expatslist.org/playa-del-carmen
Category:      expatslist.org/playa-del-carmen/restaurants
Listing:       expatslist.org/playa-del-carmen/business-name
Search:        expatslist.org/playa-del-carmen/search?q=coffee
```

## URL Patterns

### 1. City Pages
```
/playa-del-carmen                    → City homepage
/playa-del-carmen/restaurants        → Category page
/playa-del-carmen/restaurants/pizza  → Subcategory page
/playa-del-carmen/business-name      → Business listing
```

### 2. Global Pages
```
/cities                              → All cities directory
/cities/mexico                       → Cities in Mexico
/search                              → Global search
/list-business                       → Add business form
```

### 3. API Routes
```
/api/cities                          → Cities JSON
/api/cities/playa-del-carmen         → City data
/api/search                          → Search API
```

## SEO Benefits

### 1. URL Structure
- **Hierarchical**: Clear content hierarchy
- **Descriptive**: URLs describe content
- **Consistent**: Same pattern across all cities

### 2. Internal Linking
- **Breadcrumbs**: Easy navigation
- **Related content**: Cross-city recommendations
- **Category clustering**: Better topic authority

### 3. Technical SEO
- **Single domain**: Consolidated domain authority
- **Faster loading**: No DNS lookups
- **Better crawling**: Simpler for search bots

## Performance Advantages

### 1. No Middleware Overhead
- **Zero database queries** for routing
- **No subdomain validation**
- **Direct file-based routing**

### 2. Better Caching
- **CDN friendly**: Single domain caching
- **Browser caching**: Consistent domain
- **Static generation**: Pre-built pages

### 3. Simpler Infrastructure
- **Single SSL certificate**
- **One DNS record**
- **Easier deployment**

## Migration Benefits

### 1. Development
- **Simpler local development**
- **No subdomain setup needed**
- **Easier testing**

### 2. Deployment
- **Single build process**
- **No DNS configuration**
- **Simpler CI/CD**

### 3. Monitoring
- **Unified analytics**
- **Single error tracking**
- **Consolidated logs**

## Industry Examples

### Path-Based Leaders:
- **Yelp**: yelp.com/nyc/restaurants
- **TripAdvisor**: tripadvisor.com/tourism-g60763-new_york_city
- **Foursquare**: foursquare.com/explore?near=new%20york
- **Zomato**: zomato.com/new-york-city/restaurants

### Why They Choose Path-Based:
1. **Performance**: Faster page loads
2. **Scalability**: Easy to add new cities
3. **SEO**: Better overall domain authority
4. **Maintenance**: Simpler infrastructure
