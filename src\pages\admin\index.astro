---
import AdminLayout from '../../layouts/AdminLayout.astro';
import { supabase } from '../../lib/supabase';

export const prerender = false;

// Get dashboard statistics
const { data: totalListings } = await supabase
  .from('listings')
  .select('id', { count: 'exact' })
  .is('deleted_at', null);

const { data: pendingListings } = await supabase
  .from('listings')
  .select('id', { count: 'exact' })
  .eq('listing_status', 'pending_approval')
  .is('deleted_at', null);

const { data: verifiedListings } = await supabase
  .from('listings')
  .select('id', { count: 'exact' })
  .eq('is_verified_expatslist', true)
  .is('deleted_at', null);

const { data: totalCities } = await supabase
  .from('cities')
  .select('id', { count: 'exact' });

// Get recent listings for review
const { data: recentListings } = await supabase
  .from('listings')
  .select(`
    *,
    cities(name, country),
    categories(name)
  `)
  .eq('listing_status', 'pending_approval')
  .is('deleted_at', null)
  .order('created_at', { ascending: false })
  .limit(10);

// Get flagged listings
const { data: flaggedListings } = await supabase
  .from('listings')
  .select(`
    *,
    cities(name, country),
    categories(name)
  `)
  .gt('thumbs_down_count', 5)
  .is('deleted_at', null)
  .order('thumbs_down_count', { ascending: false })
  .limit(5);

const stats = {
  totalListings: totalListings?.length || 0,
  pendingListings: pendingListings?.length || 0,
  verifiedListings: verifiedListings?.length || 0,
  totalCities: totalCities?.length || 0
};
---

<AdminLayout title="Dashboard">
  <!-- Page Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
    <p class="text-gray-600 mt-2">Overview of ExpatsList platform</p>
  </div>

  <!-- Stats Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-xl shadow-md p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
          <span class="text-2xl">📋</span>
        </div>
        <div class="ml-4">
          <h3 class="text-sm font-medium text-gray-600">Total Listings</h3>
          <p class="text-2xl font-bold text-gray-900">{stats.totalListings}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-md p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
          <span class="text-2xl">⏳</span>
        </div>
        <div class="ml-4">
          <h3 class="text-sm font-medium text-gray-600">Pending Approval</h3>
          <p class="text-2xl font-bold text-gray-900">{stats.pendingListings}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-md p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
          <span class="text-2xl">✅</span>
        </div>
        <div class="ml-4">
          <h3 class="text-sm font-medium text-gray-600">Verified</h3>
          <p class="text-2xl font-bold text-gray-900">{stats.verifiedListings}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-md p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
          <span class="text-2xl">🌍</span>
        </div>
        <div class="ml-4">
          <h3 class="text-sm font-medium text-gray-600">Cities</h3>
          <p class="text-2xl font-bold text-gray-900">{stats.totalCities}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Pending Listings -->
    <div class="bg-white rounded-xl shadow-md">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">Pending Listings</h2>
          <a href="/admin/listings?status=pending" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
            View All →
          </a>
        </div>
      </div>
      
      <div class="p-6">
        {recentListings && recentListings.length > 0 ? (
          <div class="space-y-4">
            {recentListings.slice(0, 5).map((listing) => (
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900">{listing.business_name}</h3>
                  <p class="text-sm text-gray-600">{listing.cities.name} • {listing.categories.name}</p>
                  <p class="text-xs text-gray-500">
                    {new Date(listing.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div class="flex space-x-2">
                  <button 
                    onclick={`approveListing('${listing.id}')`}
                    class="px-3 py-1 text-xs font-medium text-white bg-green-600 rounded hover:bg-green-700"
                  >
                    Approve
                  </button>
                  <button 
                    onclick={`rejectListing('${listing.id}')`}
                    class="px-3 py-1 text-xs font-medium text-white bg-red-600 rounded hover:bg-red-700"
                  >
                    Reject
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p class="text-gray-500 text-center py-8">No pending listings</p>
        )}
      </div>
    </div>

    <!-- Flagged Content -->
    <div class="bg-white rounded-xl shadow-md">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">Flagged Content</h2>
          <a href="/admin/listings?flagged=true" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
            View All →
          </a>
        </div>
      </div>
      
      <div class="p-6">
        {flaggedListings && flaggedListings.length > 0 ? (
          <div class="space-y-4">
            {flaggedListings.map((listing) => (
              <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900">{listing.business_name}</h3>
                  <p class="text-sm text-gray-600">{listing.cities.name} • {listing.categories.name}</p>
                  <p class="text-xs text-red-600">
                    👎 {listing.thumbs_down_count} flags
                  </p>
                </div>
                <div class="flex space-x-2">
                  <a 
                    href={`/admin/listings/${listing.id}`}
                    class="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded hover:bg-blue-200"
                  >
                    Review
                  </a>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p class="text-gray-500 text-center py-8">No flagged content</p>
        )}
      </div>
    </div>
  </div>

  <!-- Quick Tools -->
  <div class="bg-white rounded-xl shadow-md p-6">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Tools</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <button 
        onclick="updateCityCounts()"
        class="p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
      >
        <div class="text-2xl mb-2">🔄</div>
        <h3 class="font-medium text-gray-900">Update City Counts</h3>
        <p class="text-sm text-gray-600">Refresh denormalized listing counts</p>
      </button>
      
      <a 
        href="/admin/listings/new"
        class="p-4 text-left border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors"
      >
        <div class="text-2xl mb-2">➕</div>
        <h3 class="font-medium text-gray-900">Add New Listing</h3>
        <p class="text-sm text-gray-600">Manually create a business listing</p>
      </a>
      
      <a 
        href="/admin/cities/new"
        class="p-4 text-left border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
      >
        <div class="text-2xl mb-2">🌍</div>
        <h3 class="font-medium text-gray-900">Add New City</h3>
        <p class="text-sm text-gray-600">Add a new city to the platform</p>
      </a>
    </div>
  </div>
</AdminLayout>

<script>
  async function approveListing(listingId) {
    try {
      const response = await fetch('/api/admin/approve-listing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ listingId })
      });
      
      const result = await response.json();
      
      if (result.success) {
        location.reload();
      } else {
        alert('Error approving listing: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error approving listing');
    }
  }

  async function rejectListing(listingId) {
    if (!confirm('Are you sure you want to reject this listing?')) {
      return;
    }
    
    try {
      const response = await fetch('/api/admin/reject-listing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ listingId })
      });
      
      const result = await response.json();
      
      if (result.success) {
        location.reload();
      } else {
        alert('Error rejecting listing: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error rejecting listing');
    }
  }

  async function updateCityCounts() {
    try {
      const response = await fetch('/api/admin/update-counts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      const result = await response.json();
      
      if (result.success) {
        alert('City counts updated successfully!');
        location.reload();
      } else {
        alert('Error updating counts: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error updating counts');
    }
  }
</script>
