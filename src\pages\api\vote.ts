import type { APIRoute } from 'astro';
import { supabase } from '../../lib/supabase';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { listingId, direction } = await request.json();

    if (!listingId || !direction || !['up', 'down'].includes(direction)) {
      return new Response(JSON.stringify({ error: 'Invalid request' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get current counts
    const { data: listing, error: fetchError } = await supabase
      .from('listings')
      .select('thumbs_up_count, thumbs_down_count')
      .eq('id', listingId)
      .single();

    if (fetchError || !listing) {
      return new Response(JSON.stringify({ error: 'Listing not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Update the count
    const updateData = direction === 'up' 
      ? { thumbs_up_count: listing.thumbs_up_count + 1 }
      : { thumbs_down_count: listing.thumbs_down_count + 1 };

    const { error: updateError } = await supabase
      .from('listings')
      .update(updateData)
      .eq('id', listingId);

    if (updateError) {
      console.error('Error updating vote:', updateError);
      return new Response(JSON.stringify({ error: 'Failed to update vote' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify({ 
      success: true,
      thumbsUp: direction === 'up' ? listing.thumbs_up_count + 1 : listing.thumbs_up_count,
      thumbsDown: direction === 'down' ? listing.thumbs_down_count + 1 : listing.thumbs_down_count
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
