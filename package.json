{"name": "expatslist-frontend", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/node": "^9.2.2", "@astrojs/tailwind": "^6.0.2", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/typography": "^0.5.16", "@types/pg": "^8.15.2", "astro": "^5.8.0", "clsx": "^2.1.1", "flag-icons": "^7.3.2", "lucide-react": "^0.511.0", "pg": "^8.16.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@astrojs/check": "^0.9.4", "typescript": "^5.6.3"}}