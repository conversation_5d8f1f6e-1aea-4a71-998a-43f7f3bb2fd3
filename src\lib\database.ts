import { Client } from 'pg';

// Database connection configuration - use transaction pooler
const DATABASE_URL = import.meta.env.DATABASE_URL ||
  `postgresql://postgres.ltpeowkkfassadoerorm:<EMAIL>:6543/postgres`;

// Database query function - single connection per request (Supabase pooler handles pooling)
export async function query(text: string, params?: any[]) {
  const client = new Client({
    connectionString: DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    },
    connectionTimeoutMillis: 10000,
    query_timeout: 10000,
    statement_timeout: 10000,
  });

  try {
    await client.connect();
    const result = await client.query(text, params);
    return result;
  } finally {
    await client.end();
  }
}

// Get all cities
export async function getCities() {
  try {
    const result = await query('SELECT * FROM cities ORDER BY name');
    return { data: result.rows, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get city by slug
export async function getCityBySlug(slug: string) {
  try {
    const result = await query('SELECT * FROM cities WHERE subdomain_slug = $1', [slug]);
    return { data: result.rows[0] || null, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get listings for a city
export async function getListingsForCity(cityId: string) {
  try {
    const result = await query(`
      SELECT l.*, c.name as category_name
      FROM listings l
      LEFT JOIN categories c ON l.category_primary_id = c.id
      WHERE l.city_id = $1 AND l.listing_status = 'active'
      ORDER BY l.is_pinned DESC, l.created_at DESC
    `, [cityId]);
    return { data: result.rows, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get categories
export async function getCategories() {
  try {
    const result = await query('SELECT * FROM categories ORDER BY sort_order, name');
    return { data: result.rows, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}


