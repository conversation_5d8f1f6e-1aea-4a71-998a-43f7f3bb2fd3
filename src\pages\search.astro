---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import { extractSubdomain, getCityBySubdomain } from '../lib/utils';
import { getCityBySlug, getCategories, query } from '../lib/database';

export const prerender = false; // This page needs to be server-rendered

// Get city from subdomain
const subdomain = extractSubdomain(Astro.url.hostname);
let city = null;

if (subdomain) {
  const { data: cityData } = await getCityBySlug(subdomain);
  city = cityData;
  if (!city) {
    return Astro.redirect('https://expatslist.org');
  }
} else {
  return Astro.redirect('https://expatslist.org');
}

// Get search parameters
const url = new URL(Astro.request.url);
const searchQuery = url.searchParams.get('q') || '';
const category = url.searchParams.get('category') || '';

let searchResults: any[] = [];
let categories: any[] = [];

// Get categories for filter
const { data: categoriesData } = await getCategories();
categories = categoriesData || [];

// Perform search if query exists
if (searchQuery.trim()) {
  try {
    let sql = `
      SELECT l.*, c.name as category_name, c.icon_slug as category_icon
      FROM listings l
      LEFT JOIN categories c ON l.category_primary_id = c.id
      WHERE l.city_id = $1
      AND l.listing_status = 'active'
      AND l.deleted_at IS NULL
    `;

    const params = [city.id];
    let paramIndex = 2;

    // Add category filter if specified
    if (category) {
      sql += ` AND l.category_primary_id = $${paramIndex}`;
      params.push(category);
      paramIndex++;
    }

    // Add text search (simple ILIKE for now)
    if (searchQuery.trim()) {
      sql += ` AND (
        l.business_name ILIKE $${paramIndex}
        OR l.display_name ILIKE $${paramIndex}
        OR l.description_short ILIKE $${paramIndex}
        OR l.description_long ILIKE $${paramIndex}
      )`;
      params.push(`%${searchQuery.trim()}%`);
    }

    // Order results
    sql += ` ORDER BY l.is_pinned DESC, l.is_verified_expatslist DESC, l.created_at DESC LIMIT 50`;

    const result = await query(sql, params);
    searchResults = result.rows || [];
  } catch (error) {
    console.error('Search error:', error);
    searchResults = [];
  }
}

const pageTitle = searchQuery ? `Search results for "${searchQuery}" in ${city.name}` : `Search businesses in ${city.name}`;
const pageDescription = `Search for local businesses and services in ${city.name}. Find exactly what you're looking for.`;
---

<Layout title={pageTitle} description={pageDescription} city={city.name}>
  <Header city={city} />

  <main class="min-h-screen bg-gray-50">
    <!-- Search Header -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">
          Search Businesses in {city.name}
        </h1>

        <!-- Search Form -->
        <form method="GET" class="space-y-4">
          <div class="flex flex-col md:flex-row gap-4">
            <!-- Search Input -->
            <div class="flex-1">
              <input
                type="text"
                name="q"
                value={query}
                placeholder="Search for businesses, services, or keywords..."
                class="w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <!-- Category Filter -->
            <div class="md:w-64">
              <select
                name="category"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Categories</option>
                {categories.map((cat) => (
                  <option value={cat.id} selected={category === cat.id}>
                    {cat.name}
                  </option>
                ))}
              </select>
            </div>

            <!-- Search Button -->
            <button
              type="submit"
              class="px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              Search
            </button>
          </div>
        </form>

        <!-- Search Results Summary -->
        {query && (
          <div class="mt-6 text-sm text-gray-600">
            {searchResults.length > 0 ? (
              <p>Found {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} for "{query}"</p>
            ) : (
              <p>No results found for "{query}"</p>
            )}
          </div>
        )}
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {query ? (
        searchResults.length > 0 ? (
          <!-- Search Results -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {searchResults.map((listing) => (
              <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-200">
                {listing.is_pinned && (
                  <div class="bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-t-xl">
                    ⭐ Featured
                  </div>
                )}

                <div class="p-6">
                  <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                      <h3 class="text-lg font-semibold text-gray-900 mb-2">
                        <a href={`/listing/${listing.slug}`} class="hover:text-blue-600">
                          {listing.display_name || listing.business_name}
                        </a>
                      </h3>

                      <p class="text-sm text-gray-600 mb-2">{listing.categories.name}</p>

                      {listing.description_short && (
                        <p class="text-sm text-gray-700 mb-3">{listing.description_short}</p>
                      )}

                      {listing.address_full && (
                        <p class="text-sm text-gray-600 mb-2">📍 {listing.address_full}</p>
                      )}
                    </div>

                    {listing.is_verified_expatslist && (
                      <div class="flex-shrink-0 ml-4">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          ✓ Verified
                        </span>
                      </div>
                    )}
                  </div>

                  <!-- Tags -->
                  <div class="flex flex-wrap gap-2 mb-4">
                    {listing.price_range && (
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {listing.price_range}
                      </span>
                    )}
                    {listing.languages_spoken && listing.languages_spoken.includes('English') && (
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        English
                      </span>
                    )}
                    {listing.owner_is_expat && (
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        Expat-owned
                      </span>
                    )}
                  </div>

                  <!-- Actions -->
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      {listing.contact_info?.website_url && (
                        <a
                          href={listing.contact_info.website_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          class="text-blue-600 hover:text-blue-700 text-sm font-medium"
                        >
                          Website
                        </a>
                      )}
                      {listing.contact_info?.phone && (
                        <a
                          href={`tel:${listing.contact_info.phone}`}
                          class="text-green-600 hover:text-green-700 text-sm font-medium"
                        >
                          Call
                        </a>
                      )}
                    </div>

                    <a
                      href={`/listing/${listing.slug}`}
                      class="text-blue-600 hover:text-blue-700 font-medium text-sm"
                    >
                      View Details →
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <!-- No Results -->
          <div class="text-center py-16">
            <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
              <span class="text-4xl text-gray-400">🔍</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No results found</h3>
            <p class="text-gray-600 mb-6">
              We couldn't find any businesses matching "{query}" in {city.name}.
            </p>
            <div class="space-y-3">
              <p class="text-sm text-gray-600">Try:</p>
              <ul class="text-sm text-gray-600 space-y-1">
                <li>• Using different keywords</li>
                <li>• Checking your spelling</li>
                <li>• Using more general terms</li>
                <li>• Browsing categories instead</li>
              </ul>
              <div class="mt-6">
                <a
                  href="/"
                  class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  Browse Categories
                </a>
              </div>
            </div>
          </div>
        )
      ) : (
        <!-- Search Tips -->
        <div class="max-w-3xl mx-auto">
          <div class="bg-white rounded-xl shadow-md p-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">Search Tips</h2>

            <div class="space-y-6">
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">What can you search for?</h3>
                <ul class="text-gray-700 space-y-1">
                  <li>• Business names (e.g., "Starbucks", "McDonald's")</li>
                  <li>• Service types (e.g., "dentist", "yoga", "restaurant")</li>
                  <li>• Keywords (e.g., "English speaking", "pet friendly")</li>
                  <li>• Neighborhoods (e.g., "Centro", "Playacar")</li>
                </ul>
              </div>

              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Popular Searches</h3>
                <div class="flex flex-wrap gap-2">
                  {[
                    'English speaking doctor',
                    'Real estate agent',
                    'Yoga studio',
                    'Pet friendly restaurant',
                    'Coworking space',
                    'Language school',
                    'Dentist',
                    'Massage therapy'
                  ].map((term) => (
                    <a
                      href={`/search?q=${encodeURIComponent(term)}`}
                      class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
                    >
                      {term}
                    </a>
                  ))}
                </div>
              </div>

              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Browse by Category</h3>
                <p class="text-gray-600 mb-4">Or explore our organized categories:</p>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {categories.slice(0, 6).map((category) => (
                    <a
                      href={`/category/${category.id}`}
                      class="p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                    >
                      <div class="text-sm font-medium text-gray-900">{category.name}</div>
                    </a>
                  ))}
                </div>
                <div class="mt-4">
                  <a href="/" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                    View all categories →
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  </main>
</Layout>
