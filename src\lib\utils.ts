import { clsx, type ClassValue } from 'clsx';
import { supabase, type City } from './supabase';

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * Extract subdomain from hostname
 * Returns null for main domain (expatslist.org)
 */
export function extractSubdomain(hostname: string): string | null {
  // Remove port if present
  const cleanHostname = hostname.split(':')[0];

  // For development (localhost)
  if (cleanHostname === 'localhost' || cleanHostname === '127.0.0.1') {
    return null;
  }

  // Split by dots
  const parts = cleanHostname.split('.');

  // If it's the main domain (expatslist.org) or has no subdomain
  if (parts.length <= 2) {
    return null;
  }

  // Return the first part as subdomain
  return parts[0];
}

/**
 * Get city by subdomain slug
 */
export async function getCityBySubdomain(subdomain: string): Promise<City | null> {
  const { data, error } = await supabase
    .from('cities')
    .select('*')
    .eq('subdomain_slug', subdomain)
    .single();

  if (error || !data) {
    return null;
  }

  return data;
}

/**
 * Get all cities for city selection
 */
export async function getAllCities(): Promise<City[]> {
  const { data, error } = await supabase
    .from('cities')
    .select('*')
    .order('name');

  if (error || !data) {
    return [];
  }

  return data;
}

/**
 * Generate slug from text
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Check if slug is unique within a city
 */
export async function isSlugUnique(slug: string, cityId: string, excludeId?: string): Promise<boolean> {
  let query = supabase
    .from('listings')
    .select('id')
    .eq('city_id', cityId)
    .eq('slug', slug);

  if (excludeId) {
    query = query.neq('id', excludeId);
  }

  const { data, error } = await query;

  if (error) {
    return false;
  }

  return data.length === 0;
}

/**
 * Generate unique slug for a listing
 */
export async function generateUniqueSlug(businessName: string, cityId: string, excludeId?: string): Promise<string> {
  let baseSlug = generateSlug(businessName);
  let slug = baseSlug;
  let counter = 1;

  while (!(await isSlugUnique(slug, cityId, excludeId))) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}

/**
 * Format price range display
 */
export function formatPriceRange(priceRange: string | null): string {
  if (!priceRange) return '';

  const ranges: Record<string, string> = {
    '$': 'Budget-friendly',
    '$$': 'Moderate',
    '$$$': 'Upscale',
    '$$$$': 'Luxury'
  };

  return ranges[priceRange] || priceRange;
}

/**
 * Format opening hours for display
 */
export function formatOpeningHours(openingHours: Record<string, any> | null): string {
  if (!openingHours) return 'Hours not available';

  // This is a simplified version - you might want to implement more complex logic
  if (typeof openingHours === 'string') {
    return openingHours;
  }

  // Handle structured opening hours object
  const today = new Date().toLocaleDateString('en-US', { weekday: 'short' }).toLowerCase(); // 'mon', 'tue', etc.
  const todayHours = openingHours[today];

  if (todayHours) {
    return `Today: ${todayHours}`;
  }

  return 'See full hours';
}

/**
 * Check if business is currently open
 */
export function isBusinessOpen(openingHours: Record<string, any> | null): boolean | null {
  if (!openingHours) return null;

  // Simplified implementation - you'd want more robust time parsing
  const now = new Date();
  const today = now.toLocaleDateString('en', { weekday: 'short' }).toLowerCase();
  // const currentTime = now.getHours() * 100 + now.getMinutes(); // HHMM format

  const todayHours = openingHours[today];
  if (!todayHours || todayHours === 'Closed') {
    return false;
  }

  // This would need more sophisticated parsing for real implementation
  return null; // Return null when we can't determine
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + '...';
}

/**
 * Get display name for business
 */
export function getBusinessDisplayName(listing: any): string {
  return listing.display_name || listing.business_name;
}

/**
 * Get translated content
 */
export function getTranslatedContent(
  content: string | null,
  translations: Record<string, any> | null,
  language: string = 'en'
): string {
  if (translations && translations[language]) {
    return translations[language];
  }
  return content || '';
}
