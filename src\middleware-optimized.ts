import { defineMiddleware } from 'astro:middleware';

// In-memory cache for city slugs (refreshed periodically)
let cityCache: Set<string> = new Set();
let cacheLastUpdated = 0;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

async function refreshCityCache() {
  try {
    // Only import when needed to avoid cold start penalty
    const { getCities } = await import('./lib/database');
    const { data: cities } = await getCities();
    
    if (cities) {
      cityCache = new Set(cities.map(city => city.subdomain_slug));
      cacheLastUpdated = Date.now();
      console.log('🔄 City cache refreshed:', cityCache.size, 'cities');
    }
  } catch (error) {
    console.error('Failed to refresh city cache:', error);
  }
}

export const onRequest = defineMiddleware(async (context, next) => {
  const { url } = context;
  const hostname = url.hostname;

  // Skip middleware for main domain
  if (hostname === 'expatslist.org' || hostname === 'www.expatslist.org' || !hostname.endsWith('.expatslist.org')) {
    return next();
  }

  // Extract subdomain
  const subdomain = hostname.replace('.expatslist.org', '');

  // Refresh cache if needed (non-blocking for first request)
  if (Date.now() - cacheLastUpdated > CACHE_TTL) {
    // Don't await - refresh in background
    refreshCityCache().catch(console.error);
  }

  // Check cache first (fast path)
  if (cityCache.has(subdomain)) {
    if (url.pathname === '/' || url.pathname === '') {
      return context.rewrite(`/city/${subdomain}`);
    }
    return next();
  }

  // If cache is empty (first request), do synchronous lookup
  if (cityCache.size === 0) {
    await refreshCityCache();
    if (cityCache.has(subdomain)) {
      if (url.pathname === '/' || url.pathname === '') {
        return context.rewrite(`/city/${subdomain}`);
      }
      return next();
    }
  }

  // Invalid subdomain - redirect to main site
  return context.redirect('https://expatslist.org', 301);
});
