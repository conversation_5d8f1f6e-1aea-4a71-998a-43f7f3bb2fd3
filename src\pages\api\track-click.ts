import type { APIRoute } from 'astro';
import { supabase } from '../../lib/supabase';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { listingId, type } = await request.json();

    if (!listingId || !type) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Update the appropriate click count
    const updateField = type === 'website' 
      ? 'click_through_count_website' 
      : 'click_through_count_phone';

    const { error } = await supabase
      .rpc('increment_click_count', {
        listing_id: listingId,
        click_type: updateField
      });

    if (error) {
      console.error('Error tracking click:', error);
      return new Response(JSON.stringify({ error: 'Failed to track click' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
