---
import Layout from '../../layouts/Layout.astro';
import { supabase } from '../../lib/supabase';

// Simulate Playa del Carmen city data
const city = {
  id: 'playa-id',
  name: 'Playa del Carmen',
  subdomain_slug: 'playadelcarmen',
  country: 'Mexico',
  total_listing_count: 3,
  verified_listing_count: 1
};

// Define categories with icons, colors, and subcategories
const categories = [
  {
    name: 'Housing & Real Estate',
    icon: '🏠',
    color: 'bg-blue-50 hover:bg-blue-100',
    subcategories: ['Airbnbs & Vacation Rentals', 'Apartment Rentals', 'Co-Living & Flatshares', 'Furnished Rentals', 'Property Management', 'Real Estate Agents']
  },
  {
    name: 'Wellness & Fitness',
    icon: '🧘‍♀️',
    color: 'bg-green-50 hover:bg-green-100',
    subcategories: ['Alternative Medicine', 'Gyms & Personal Trainers', 'Massage & Bodywork', 'Mental Health & Therapy', 'Spas & Beauty Salons', 'Yoga Studios']
  },
  {
    name: 'Food & Dining',
    icon: '🍽️',
    color: 'bg-orange-50 hover:bg-orange-100',
    subcategories: ['Butchers, Bakeries, Seafood', 'Cafés & Coffee Shops', 'Delivery & Takeaway', 'Grocery Stores & Organic Markets', 'Restaurants by Cuisine', 'Street Food & Local Eats', 'Vegan / Vegetarian Spots']
  },
  {
    name: 'Health & Medical',
    icon: '🧑‍⚕️',
    color: 'bg-red-50 hover:bg-red-100',
    subcategories: ['Clinics & Hospitals', 'Dentists', 'Emergency Services', 'Health Insurance Brokers', 'Optometrists', 'Pharmacies']
  },
  {
    name: 'Jobs & Services',
    icon: '💼',
    color: 'bg-purple-50 hover:bg-purple-100',
    subcategories: ['Business Services', 'Coworking Spaces', 'English Teaching Centers', 'Job Boards', 'Legal & Immigration Services', 'Marketing & Tech Agencies', 'Recruitment Agencies']
  },
  {
    name: 'Relocation & Immigration',
    icon: '✈️',
    color: 'bg-indigo-50 hover:bg-indigo-100',
    subcategories: ['Cultural Orientation Services', 'Language Schools', 'Moving & Shipping Companies', 'Real Estate for Expats', 'SIM Cards & Phone Plans', 'Visa & Immigration Lawyers']
  },
  {
    name: 'Shopping & Essentials',
    icon: '🛍️',
    color: 'bg-pink-50 hover:bg-pink-100',
    subcategories: ['Clothing & Accessories', 'Electronics & Repairs', 'Furniture & Appliances', 'Malls & Shopping Centers', 'Second-Hand Stores']
  },
  {
    name: 'Education & Classes',
    icon: '🧑‍🎓',
    color: 'bg-yellow-50 hover:bg-yellow-100',
    subcategories: ['After-School Programs', 'Cooking & Art Classes', 'International Schools', 'Online Courses & Workshops', 'Spanish Language Schools', 'Surf & Diving Schools']
  },
  {
    name: 'Transport & Mobility',
    icon: '🚗',
    color: 'bg-gray-50 hover:bg-gray-100',
    subcategories: ['Airport Transfers & Shuttles', 'Auto Repair & Mechanics', 'Bicycle Rentals & Sales', 'Car & Scooter Rentals', 'Public Transportation Info', 'Taxi & Rideshare Services']
  },
  {
    name: 'Pets & Animals',
    icon: '🐶',
    color: 'bg-emerald-50 hover:bg-emerald-100',
    subcategories: ['Pet Adoption & Rescue', 'Pet Grooming & Boarding', 'Pet Stores & Food', 'Veterinarians']
  },
  {
    name: 'Spirituality & Community',
    icon: '🧘‍♂️',
    color: 'bg-violet-50 hover:bg-violet-100',
    subcategories: ['AA / NA / Support Groups', 'Churches, Temples & Mosques', 'Expat Social Clubs', 'Local Events', 'Meditation Centers', 'Volunteer Opportunities']
  },
  {
    name: 'Leisure & Experiences',
    icon: '🏖️',
    color: 'bg-cyan-50 hover:bg-cyan-100',
    subcategories: ['Adventure Sports', 'Beach Clubs & Resorts', 'Boat Rentals', 'Day Trips', 'Local Guides & Activities', 'Tours & Travel Agencies']
  },
  {
    name: 'Financial & Legal',
    icon: '🧾',
    color: 'bg-slate-50 hover:bg-slate-100',
    subcategories: ['Banks & ATMs', 'Currency Exchange', 'Insurance Agents', 'Notaries & Legal Support', 'Tax Advisors']
  },
  {
    name: 'Tech & Connectivity',
    icon: '💻',
    color: 'bg-blue-50 hover:bg-blue-100',
    subcategories: ['Computer & Phone Repair', 'Coworking Space Reviews', 'Internet Providers & Packages', 'SIM Cards & Mobile Plans', 'Tech Gear Shops', 'VPN Services']
  },
  {
    name: 'Family & Kids',
    icon: '👶',
    color: 'bg-rose-50 hover:bg-rose-100',
    subcategories: ['Daycares & Babysitters', 'Family Events & Activities', 'Family-Friendly Restaurants', 'Parenting Support Groups', 'Pediatricians', 'Toy Stores & Kids Gear']
  },
  {
    name: 'Travel & Weekend Trips',
    icon: '🧳',
    color: 'bg-teal-50 hover:bg-teal-100',
    subcategories: ['Bus & ADO Routes', 'Cruise Port Info', 'Eco Tours & Cenotes', 'Local Travel Agencies', 'Nearby Destinations', 'Visa Runs to Belize or Guatemala']
  },
  {
    name: 'Home Services',
    icon: '🛠️',
    color: 'bg-amber-50 hover:bg-amber-100',
    subcategories: ['A/C Installation & Repair', 'Cleaning Services', 'Electricians & Plumbers', 'Gardeners & Pool Maintenance', 'Interior Designers', 'Pest Control']
  },
  {
    name: 'Creative & Art Scene',
    icon: '🎨',
    color: 'bg-fuchsia-50 hover:bg-fuchsia-100',
    subcategories: ['Art Galleries', 'Art Supply Shops', 'Local Artists & Artisans', 'Music Lessons & Studios', 'Performance Venues', 'Photography Services']
  },
  {
    name: 'Nightlife & Entertainment',
    icon: '🎉',
    color: 'bg-purple-50 hover:bg-purple-100',
    subcategories: ['Bars & Lounges', 'Clubs & DJs', 'Karaoke / Open Mic Nights', 'Ladies Nights & Happy Hours', 'Live Music Venues']
  },
  {
    name: 'Language & Culture',
    icon: '🎓',
    color: 'bg-lime-50 hover:bg-lime-100',
    subcategories: ['Cooking Classes', 'Dance Classes', 'Language Exchange Meetups', 'Mayan Culture Tours', 'Spanish Tutors']
  },
  {
    name: 'Government & Civic',
    icon: '🛂',
    color: 'bg-stone-50 hover:bg-stone-100',
    subcategories: ['INM Offices', 'Municipality Services', 'Public Services Info', 'Tax & SAT Offices', 'Voting Assistance for Foreigners']
  },
  {
    name: 'Emergency & Safety',
    icon: '🆘',
    color: 'bg-red-50 hover:bg-red-100',
    subcategories: ['Emergency Numbers', 'Local Embassies/Consulates', 'Lost & Found', 'Medical Emergency Services', 'Police Stations', 'Safety Tips & Alerts']
  }
];

// Get sample listings for Playa del Carmen
const { data: featuredListings } = await supabase
  .from('listings')
  .select(`
    *,
    categories!inner(name, icon_slug)
  `)
  .eq('city_id', (await supabase.from('cities').select('id').eq('subdomain_slug', 'playadelcarmen').single()).data?.id)
  .eq('listing_status', 'active')
  .is('deleted_at', null)
  .order('is_pinned', { ascending: false })
  .order('is_verified_expatslist', { ascending: false })
  .order('created_at', { ascending: false })
  .limit(12);

const pageTitle = `Find Local Businesses in ${city.name}`;
const pageDescription = `Discover trusted local businesses and services in ${city.name}. From housing and healthcare to dining and entertainment - find everything you need as an expat.`;
---

<Layout title={pageTitle} description={pageDescription} city={city.name}>
  <main class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{city.name}</h1>
            <p class="text-gray-600 mt-1">Find local businesses and services</p>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-right text-sm text-gray-500">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </div>
            <a href="/" class="text-blue-600 hover:underline">← All cities</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Categories Grid -->
    <div class="max-w-7xl mx-auto px-4 py-8">
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4" id="categories-grid">
        {categories.map((category, index) => (
          <div class="category-container">
            <!-- Category Card -->
            <div
              class={`category-card ${category.color} p-4 rounded-lg border border-gray-200 transition-all duration-300 cursor-pointer hover:shadow-md`}
              data-category-index={index}
            >
              <div class="text-center">
                <div class="text-3xl mb-2">{category.icon}</div>
                <h3 class="text-sm font-medium text-gray-900 leading-tight mb-1">
                  {category.name}
                </h3>
                <div class="text-xs text-gray-500 mb-2">
                  {category.subcategories.length} subcategories
                </div>
                <div class="expand-indicator text-gray-400 text-xs transition-transform duration-300">
                  <span class="chevron">▼</span>
                </div>
              </div>
            </div>

            <!-- Subcategories Panel (Hidden by default) -->
            <div
              id={`subcategories-${index}`}
              class="subcategories-panel hidden mt-2 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden transition-all duration-300"
            >
              <div class="p-4">
                <div class="flex items-center justify-between mb-3">
                  <h4 class="font-medium text-gray-900 flex items-center">
                    <span class="mr-2">{category.icon}</span>
                    {category.name}
                  </h4>
                  <button class="close-btn text-gray-400 hover:text-gray-600" data-category-index={index}>
                    ✕
                  </button>
                </div>
                <div class="space-y-2">
                  {category.subcategories.map((subcategory) => (
                    <a
                      href={`/subcategory/${subcategory.toLowerCase().replace(/[^a-z0-9]+/g, '-')}`}
                      class="block text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-2 py-1 rounded transition-colors duration-200"
                    >
                      {subcategory}
                    </a>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>

    <style>
      .category-card.expanded {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .category-card.expanded .chevron {
        transform: rotate(180deg);
      }

      .subcategories-panel.show {
        display: block;
        animation: slideDown 0.3s ease-out;
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .chevron {
        display: inline-block;
        transition: transform 0.3s ease;
      }
    </style>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const grid = document.getElementById('categories-grid');

        // Handle category card clicks
        grid.addEventListener('click', function(e) {
          const card = e.target.closest('.category-card');
          if (!card) return;

          const index = card.dataset.categoryIndex;
          const panel = document.getElementById(`subcategories-${index}`);
          const isExpanded = card.classList.contains('expanded');

          // Close all other panels
          document.querySelectorAll('.category-card.expanded').forEach(otherCard => {
            if (otherCard !== card) {
              otherCard.classList.remove('expanded');
              const otherIndex = otherCard.dataset.categoryIndex;
              const otherPanel = document.getElementById(`subcategories-${otherIndex}`);
              otherPanel.classList.remove('show');
              setTimeout(() => otherPanel.classList.add('hidden'), 300);
            }
          });

          // Toggle current panel
          if (isExpanded) {
            card.classList.remove('expanded');
            panel.classList.remove('show');
            setTimeout(() => panel.classList.add('hidden'), 300);
          } else {
            card.classList.add('expanded');
            panel.classList.remove('hidden');
            setTimeout(() => panel.classList.add('show'), 10);
          }
        });

        // Handle close button clicks
        grid.addEventListener('click', function(e) {
          if (e.target.classList.contains('close-btn')) {
            e.stopPropagation();
            const index = e.target.dataset.categoryIndex;
            const card = document.querySelector(`[data-category-index="${index}"]`);
            const panel = document.getElementById(`subcategories-${index}`);

            card.classList.remove('expanded');
            panel.classList.remove('show');
            setTimeout(() => panel.classList.add('hidden'), 300);
          }
        });
      });
    </script>

    <!-- Recent Listings Section -->
    {featuredListings && featuredListings.length > 0 && (
      <div class="max-w-7xl mx-auto px-4 pb-8">
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Listings</h2>
          <div class="space-y-3">
            {featuredListings.slice(0, 5).map((listing) => (
              <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900">
                    <a href={`/listing/${listing.slug}`} class="hover:text-blue-600">
                      {listing.display_name || listing.business_name}
                    </a>
                  </h3>
                  <p class="text-sm text-gray-600">{listing.categories.name}</p>
                </div>
                <div class="text-right">
                  {listing.is_verified_expatslist && (
                    <span class="inline-block px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                      Verified
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )}
  </main>
</Layout>
