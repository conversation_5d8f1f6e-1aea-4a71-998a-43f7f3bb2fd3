# ExpatsList Deployment Guide

## 🚀 Deploying with <PERSON>k<PERSON><PERSON> + Nixpacks + Docker

### Prerequisites

1. **Linux Server** with Docker installed
2. **Dokploy** installed on your server
3. **Domain name** pointed to your server
4. **Supabase project** with database setup

### Step 1: Server Setup

```bash
# Install Docker (if not already installed)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Dokploy
curl -sSL https://dokploy.com/install.sh | sh
```

### Step 2: Prepare Your Repository

1. **Push your code** to a Git repository (GitHub, GitLab, etc.)
2. **Ensure all files are committed**:
   - `package.json`
   - `nixpacks.toml`
   - `Dockerfile`
   - `astro.config.mjs`

### Step 3: Dokploy Configuration

1. **Access Dokploy Dashboard**:
   ```
   http://your-server-ip:3000
   ```

2. **Create New Application**:
   - Click "Create Application"
   - Choose "Git Repository"
   - Connect your repository

3. **Configure Build Settings**:
   - **Build Pack**: Select "Nixpacks"
   - **Build Command**: `npm run build`
   - **Start Command**: `node ./dist/server/entry.mjs`
   - **Port**: `4321`

### Step 4: Environment Variables

Set these environment variables in Dokploy:

```env
NODE_ENV=production
HOST=0.0.0.0
PORT=4321
PUBLIC_SUPABASE_URL=your_supabase_project_url
PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Step 5: Domain Configuration

1. **Add Domain** in Dokploy:
   - Go to your application settings
   - Add your domain (e.g., `expatslist.com`)
   - Enable SSL/TLS certificate

2. **DNS Configuration**:
   ```
   A Record: @ -> your-server-ip
   A Record: www -> your-server-ip
   ```

### Step 6: Deploy

1. **Trigger Deployment**:
   - Click "Deploy" in Dokploy dashboard
   - Monitor build logs
   - Wait for deployment to complete

2. **Verify Deployment**:
   - Visit your domain
   - Check application functionality
   - Monitor logs for any issues

### Alternative: Manual Docker Deployment

If you prefer manual Docker deployment:

```bash
# Clone repository
git clone your-repo-url
cd expatslist

# Create .env file
cp .env.example .env
# Edit .env with your values

# Build Docker image
docker build -t expatslist .

# Run container
docker run -d \
  --name expatslist \
  -p 4321:4321 \
  --env-file .env \
  expatslist
```

### Troubleshooting

**Build Fails**:
- Check Node.js version compatibility
- Verify all dependencies are in package.json
- Check build logs for specific errors

**Application Won't Start**:
- Verify environment variables
- Check Supabase connection
- Review application logs

**Performance Issues**:
- Enable Docker resource limits
- Monitor server resources
- Consider using a reverse proxy (Nginx)

### Production Optimizations

1. **Reverse Proxy Setup** (Nginx):
```nginx
server {
    listen 80;
    server_name expatslist.com www.expatslist.com;
    
    location / {
        proxy_pass http://localhost:4321;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

2. **SSL Certificate**:
```bash
# Using Certbot
sudo certbot --nginx -d expatslist.com -d www.expatslist.com
```

3. **Monitoring**:
- Set up application monitoring
- Configure log aggregation
- Monitor server resources

### Backup Strategy

1. **Database Backups**: Supabase handles this automatically
2. **Application Backups**: Use Git for code versioning
3. **Environment Backups**: Store environment variables securely

### Scaling Considerations

- **Horizontal Scaling**: Deploy multiple instances behind a load balancer
- **Database Scaling**: Supabase handles this automatically
- **CDN**: Consider using a CDN for static assets
- **Caching**: Implement Redis for session/data caching

For support, check the Dokploy documentation or create an issue in the repository.
