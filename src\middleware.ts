import { defineMiddleware } from 'astro:middleware';
import { getCityBySlug } from './lib/database';

export const onRequest = defineMiddleware(async (context, next) => {
  const { url } = context;
  const hostname = url.hostname;

  console.log('🔍 Middleware - hostname:', hostname, 'pathname:', url.pathname, 'full URL:', url.toString());

  // Check if this is a subdomain request (and not already rewritten)
  if (hostname !== 'expatslist.org' && hostname !== 'www.expatslist.org' && hostname.endsWith('.expatslist.org') && !url.pathname.startsWith('/city/')) {
    // Extract subdomain (e.g., 'playadelcarmen' from 'playadelcarmen.expatslist.org')
    const subdomain = hostname.replace('.expatslist.org', '');

    console.log('Subdomain detected:', subdomain);

    // Check if this subdomain exists as a city in the database
    try {
      const { data: city, error } = await getCityBySlug(subdomain);

      if (city && !error) {
        console.log('Valid city subdomain found:', subdomain, city.name);

        // If requesting root path, rewrite to city page
        if (url.pathname === '/' || url.pathname === '') {
          console.log('Rewriting subdomain root to city page:', subdomain);

          // Rewrite the URL internally
          const newUrl = new URL(`/city/${subdomain}`, url);
          context.url = newUrl;

          console.log('Rewritten URL:', newUrl.toString());
          console.log('New pathname:', newUrl.pathname);
        }
      } else {
        console.log('Invalid city subdomain:', subdomain);
      }
    } catch (error) {
      console.error('Error checking city subdomain:', error);
    }
  }

  return next();
});
