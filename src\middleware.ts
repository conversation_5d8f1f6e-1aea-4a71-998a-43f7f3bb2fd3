import { defineMiddleware } from 'astro:middleware';

// In-memory cache for city slugs
let cityCache: Set<string> = new Set();
let cacheLastUpdated = 0;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

async function refreshCityCache() {
  try {
    const { getCityBySlug, getCities } = await import('./lib/database');
    const { data: cities } = await getCities();

    if (cities) {
      cityCache = new Set(cities.map(city => city.subdomain_slug));
      cacheLastUpdated = Date.now();
      console.log('🔄 City cache refreshed:', cityCache.size, 'cities');
    }
  } catch (error) {
    console.error('Failed to refresh city cache:', error);
  }
}

export const onRequest = defineMiddleware(async (context, next) => {
  const { url } = context;
  const hostname = url.hostname;

  // Skip middleware for main domain and non-subdomain requests
  if (hostname === 'expatslist.org' || hostname === 'www.expatslist.org' || !hostname.endsWith('.expatslist.org')) {
    return next();
  }

  // Extract subdomain
  const subdomain = hostname.replace('.expatslist.org', '');

  // Refresh cache if needed (background refresh)
  if (Date.now() - cacheLastUpdated > CACHE_TTL) {
    refreshCityCache().catch(console.error);
  }

  // Initialize cache on first request
  if (cityCache.size === 0) {
    await refreshCityCache();
  }

  // Check cache first (fast path)
  if (cityCache.has(subdomain)) {
    if (url.pathname === '/' || url.pathname === '') {
      return context.rewrite(`/city/${subdomain}`);
    }
    return next();
  }

  // Invalid subdomain - redirect to main site
  console.log('Invalid city subdomain:', subdomain);
  return context.redirect('https://expatslist.org', 301);
});
