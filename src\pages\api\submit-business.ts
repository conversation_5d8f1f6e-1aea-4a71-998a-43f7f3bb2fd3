import type { APIRoute } from 'astro';
import { supabase } from '../../lib/supabase';
import { generateUniqueSlug } from '../../lib/utils';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();

    // Validate required fields
    const requiredFields = ['business_name', 'city_id', 'category_primary_id', 'description_short'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return new Response(JSON.stringify({
          error: `Missing required field: ${field}`
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // Generate unique slug
    const slug = await generateUniqueSlug(data.business_name, data.city_id);

    // Prepare contact info object
    const contactInfo: any = {};
    if (data.phone) contactInfo.phone = data.phone;
    if (data.email) contactInfo.email = data.email;
    if (data.website_url) contactInfo.website_url = data.website_url;

    // Prepare listing data
    const listingData = {
      slug,
      business_name: data.business_name,
      display_name: data.display_name || null,
      city_id: data.city_id,
      category_primary_id: data.category_primary_id,
      description_short: data.description_short,
      description_long: data.description_long || null,
      contact_info: Object.keys(contactInfo).length > 0 ? contactInfo : null,
      address_full: data.address_full || null,
      languages_spoken: data.languages_spoken || null,
      services_offered_keywords: data.services_offered || null,
      price_range: data.price_range || null,
      owner_is_expat: data.owner_is_expat || false,
      pet_friendly: data.pet_friendly || false,
      kid_friendly: data.kid_friendly || false,
      listing_status: 'pending_approval',
      is_verified_expatslist: false,
      is_pinned: false,
      view_count: 0,
      click_through_count_website: 0,
      click_through_count_phone: 0,
      thumbs_up_count: 0,
      thumbs_down_count: 0
    };

    // Insert the listing
    const { data: insertedListing, error } = await supabase
      .from('listings')
      .insert([listingData])
      .select()
      .single();

    if (error) {
      console.error('Error inserting listing:', error);
      return new Response(JSON.stringify({
        error: 'Failed to submit business listing'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Update city counts (fire and forget)
    supabase.rpc('update_city_listing_counts', { target_city_id: data.city_id })
      .then(() => {})
      .catch((error) => console.error(error));

    return new Response(JSON.stringify({
      success: true,
      listingId: insertedListing.id,
      slug: insertedListing.slug
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
