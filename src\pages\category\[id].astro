---
import Layout from '../../layouts/Layout.astro';
import Header from '../../components/Header.astro';
import { extractSubdomain, getCityBySubdomain } from '../../lib/utils';
import { supabase } from '../../lib/supabase';

export const prerender = false; // This page needs to be server-rendered

// Get city from subdomain
const subdomain = extractSubdomain(Astro.url.hostname);
let city = null;

if (subdomain) {
  city = await getCityBySubdomain(subdomain);
  if (!city) {
    return Astro.redirect('https://expatslist.org');
  }
} else {
  // Redirect to main site if no city subdomain
  return Astro.redirect('https://expatslist.org');
}

// Get category ID from URL
const { id: categoryId } = Astro.params;

// Fetch category details
const { data: category, error: categoryError } = await supabase
  .from('categories')
  .select('*')
  .eq('id', categoryId)
  .single();

if (categoryError || !category) {
  return Astro.redirect('/');
}

// Fetch listings for this category and city
const { data: listings, error: listingsError } = await supabase
  .from('listings')
  .select(`
    *,
    categories!inner(name, icon_slug)
  `)
  .eq('city_id', city.id)
  .eq('category_primary_id', categoryId)
  .eq('listing_status', 'active')
  .is('deleted_at', null)
  .order('is_pinned', { ascending: false })
  .order('is_verified_expatslist', { ascending: false })
  .order('created_at', { ascending: false });

const categoryListings = listings || [];

const pageTitle = `${category.name} in ${city.name}`;
const pageDescription = `Find the best ${category.name.toLowerCase()} services in ${city.name}. Verified businesses and services for expats.`;
---

<Layout title={pageTitle} description={pageDescription} city={city.name}>
  <Header city={city} />
  
  <main class="min-h-screen bg-gray-50">
    <!-- Category Header -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex items-center space-x-4">
          <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center">
            <span class="text-3xl">{category.icon_slug ? '📍' : '🏢'}</span>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{category.name}</h1>
            <p class="text-lg text-gray-600 mt-1">in {city.name}</p>
            {category.description && (
              <p class="text-gray-700 mt-2">{category.description}</p>
            )}
          </div>
        </div>
        
        <!-- Stats -->
        <div class="mt-6 flex items-center space-x-8 text-sm text-gray-600">
          <span>{categoryListings.length} businesses found</span>
          <span>{categoryListings.filter(l => l.is_verified_expatslist).length} verified</span>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <!-- Search -->
          <div class="flex-1 max-w-lg">
            <div class="relative">
              <input 
                type="text" 
                placeholder={`Search ${category.name.toLowerCase()}...`}
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </button>
            </div>
          </div>
          
          <!-- Sort -->
          <div class="flex items-center space-x-4">
            <label class="text-sm font-medium text-gray-700">Sort by:</label>
            <select class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="featured">Featured First</option>
              <option value="verified">Verified First</option>
              <option value="newest">Newest First</option>
              <option value="name">Name A-Z</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Listings Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {categoryListings.length > 0 ? (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categoryListings.map((listing) => (
            <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-200">
              {listing.is_pinned && (
                <div class="bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-t-xl">
                  ⭐ Featured
                </div>
              )}
              
              <div class="p-6">
                <div class="flex items-start justify-between mb-4">
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                      <a href={`/listing/${listing.slug}`} class="hover:text-blue-600">
                        {listing.display_name || listing.business_name}
                      </a>
                    </h3>
                    
                    {listing.description_short && (
                      <p class="text-sm text-gray-700 mb-3">{listing.description_short}</p>
                    )}
                    
                    {listing.address_full && (
                      <p class="text-sm text-gray-600 mb-2">📍 {listing.address_full}</p>
                    )}
                  </div>
                  
                  {listing.is_verified_expatslist && (
                    <div class="flex-shrink-0 ml-4">
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        ✓ Verified
                      </span>
                    </div>
                  )}
                </div>
                
                <!-- Tags and Info -->
                <div class="flex flex-wrap gap-2 mb-4">
                  {listing.price_range && (
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {listing.price_range}
                    </span>
                  )}
                  {listing.languages_spoken && listing.languages_spoken.includes('English') && (
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      English
                    </span>
                  )}
                  {listing.owner_is_expat && (
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      Expat-owned
                    </span>
                  )}
                </div>
                
                <!-- Contact Actions -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    {listing.contact_info?.website_url && (
                      <a 
                        href={listing.contact_info.website_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        class="text-blue-600 hover:text-blue-700 text-sm font-medium"
                      >
                        Website
                      </a>
                    )}
                    {listing.contact_info?.phone && (
                      <a 
                        href={`tel:${listing.contact_info.phone}`}
                        class="text-green-600 hover:text-green-700 text-sm font-medium"
                      >
                        Call
                      </a>
                    )}
                  </div>
                  
                  <a 
                    href={`/listing/${listing.slug}`}
                    class="text-blue-600 hover:text-blue-700 font-medium text-sm"
                  >
                    View Details →
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <!-- Empty State -->
        <div class="text-center py-16">
          <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
            <span class="text-4xl text-gray-400">🔍</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">No businesses found</h3>
          <p class="text-gray-600 mb-6">
            We don't have any {category.name.toLowerCase()} listings in {city.name} yet.
          </p>
          <div class="space-y-3">
            <a 
              href="/list-business" 
              class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              List Your Business
            </a>
            <div>
              <a href="/" class="text-blue-600 hover:text-blue-700 font-medium">
                ← Browse other categories
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  </main>
</Layout>
