---
import '../styles/global.css';

export interface Props {
	title: string;
	description?: string;
	city?: string;
	canonical?: string;
}

const { title, description = "Find trusted local businesses and services for expats worldwide", city, canonical } = Astro.props;

const fullTitle = city ? `${title} - ${city} | ExpatsList` : `${title} | ExpatsList`;
const canonicalUrl = canonical || Astro.url.href;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{fullTitle}</title>

		<!-- Canonical URL -->
		<link rel="canonical" href={canonicalUrl} />

		<!-- Open Graph -->
		<meta property="og:title" content={fullTitle} />
		<meta property="og:description" content={description} />
		<meta property="og:type" content="website" />
		<meta property="og:url" content={canonicalUrl} />
		<meta property="og:site_name" content="ExpatsList" />

		<!-- Twitter Card -->
		<meta name="twitter:card" content="summary_large_image" />
		<meta name="twitter:title" content={fullTitle} />
		<meta name="twitter:description" content={description} />

		<!-- Preconnect to external domains -->
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

		<!-- Google Fonts -->
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

		<!-- Flag Icons -->
		<link rel="stylesheet" href="/custom-flags.css" />

	</head>
	<body class="min-h-screen bg-gray-50 font-sans antialiased">
		<slot />
	</body>
</html>

<style is:global>
	html {
		font-family: 'Inter', system-ui, sans-serif;
		scroll-behavior: smooth;
	}

	body {
		line-height: 1.6;
	}

	/* Custom scrollbar */
	::-webkit-scrollbar {
		width: 8px;
	}

	::-webkit-scrollbar-track {
		background: #f1f5f9;
	}

	::-webkit-scrollbar-thumb {
		background: #cbd5e1;
		border-radius: 4px;
	}

	::-webkit-scrollbar-thumb:hover {
		background: #94a3b8;
	}
</style>
