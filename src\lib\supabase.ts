import { createClient, type SupabaseClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.PUBLIC_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Create client with REST API disabled for server-side use
export const supabase: SupabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
  db: {
    schema: 'public'
  },
  auth: {
    persistSession: false
  }
});

// Database types (generated from Supabase)
export interface Database {
  public: {
    Tables: {
      cities: {
        Row: {
          id: string;
          name: string;
          subdomain_slug: string;
          country: string | null;
          total_listing_count: number;
          verified_listing_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          subdomain_slug: string;
          country?: string | null;
          total_listing_count?: number;
          verified_listing_count?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          subdomain_slug?: string;
          country?: string | null;
          total_listing_count?: number;
          verified_listing_count?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          icon_slug: string | null;
          parent_id: string | null;
          description: string | null;
          sort_order: number | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          icon_slug?: string | null;
          parent_id?: string | null;
          description?: string | null;
          sort_order?: number | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          icon_slug?: string | null;
          parent_id?: string | null;
          description?: string | null;
          sort_order?: number | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      listings: {
        Row: {
          id: string;
          slug: string;
          business_name: string;
          name_translations: Record<string, any>;
          display_name: string | null;
          category_primary_id: string;
          categories_secondary_ids: string[] | null;
          description_short: string | null;
          description_short_translations: Record<string, any>;
          description_long: string | null;
          description_long_translations: Record<string, any>;
          contact_info: Record<string, any> | null;
          address_full: string | null;
          address_components: Record<string, any> | null;
          geolocation: unknown | null;
          opening_hours: Record<string, any> | null;
          main_image_path: string | null;
          image_gallery_paths: string[] | null;
          languages_spoken: string[] | null;
          services_offered_keywords: string[] | null;
          expat_friendly_rating: number | null;
          target_audience_keywords: string[] | null;
          payment_methods_accepted: string[] | null;
          wifi_availability: string | null;
          pet_friendly: boolean | null;
          kid_friendly: boolean | null;
          accessibility_features: string[] | null;
          price_range: string | null;
          special_offers_for_expats: string | null;
          owner_is_expat: boolean | null;
          years_in_business_in_city: number | null;
          certifications_licenses: string[] | null;
          testimonials_quotes_crawled: Record<string, any> | null;
          city_id: string;
          owner_user_id: string | null;
          is_verified_expatslist: boolean;
          claimed_by_user_id: string | null;
          listing_status: string;
          source_urls_crawled: string[] | null;
          last_crawled_at: string | null;
          is_pinned: boolean;
          pinned_until: string | null;
          view_count: number;
          click_through_count_website: number;
          click_through_count_phone: number;
          thumbs_up_count: number;
          thumbs_down_count: number;
          last_updated_by_owner_at: string | null;
          last_updated_by_admin_at: string | null;
          admin_notes: string | null;
          created_at: string;
          updated_at: string;
          deleted_at: string | null;
        };
        Insert: {
          id?: string;
          slug: string;
          business_name: string;
          name_translations?: Record<string, any>;
          display_name?: string | null;
          category_primary_id: string;
          categories_secondary_ids?: string[] | null;
          description_short?: string | null;
          description_short_translations?: Record<string, any>;
          description_long?: string | null;
          description_long_translations?: Record<string, any>;
          contact_info?: Record<string, any> | null;
          address_full?: string | null;
          address_components?: Record<string, any> | null;
          geolocation?: unknown | null;
          opening_hours?: Record<string, any> | null;
          main_image_path?: string | null;
          image_gallery_paths?: string[] | null;
          languages_spoken?: string[] | null;
          services_offered_keywords?: string[] | null;
          expat_friendly_rating?: number | null;
          target_audience_keywords?: string[] | null;
          payment_methods_accepted?: string[] | null;
          wifi_availability?: string | null;
          pet_friendly?: boolean | null;
          kid_friendly?: boolean | null;
          accessibility_features?: string[] | null;
          price_range?: string | null;
          special_offers_for_expats?: string | null;
          owner_is_expat?: boolean | null;
          years_in_business_in_city?: number | null;
          certifications_licenses?: string[] | null;
          testimonials_quotes_crawled?: Record<string, any> | null;
          city_id: string;
          owner_user_id?: string | null;
          is_verified_expatslist?: boolean;
          claimed_by_user_id?: string | null;
          listing_status?: string;
          source_urls_crawled?: string[] | null;
          last_crawled_at?: string | null;
          is_pinned?: boolean;
          pinned_until?: string | null;
          view_count?: number;
          click_through_count_website?: number;
          click_through_count_phone?: number;
          thumbs_up_count?: number;
          thumbs_down_count?: number;
          last_updated_by_owner_at?: string | null;
          last_updated_by_admin_at?: string | null;
          admin_notes?: string | null;
          created_at?: string;
          updated_at?: string;
          deleted_at?: string | null;
        };
        Update: {
          id?: string;
          slug?: string;
          business_name?: string;
          name_translations?: Record<string, any>;
          display_name?: string | null;
          category_primary_id?: string;
          categories_secondary_ids?: string[] | null;
          description_short?: string | null;
          description_short_translations?: Record<string, any>;
          description_long?: string | null;
          description_long_translations?: Record<string, any>;
          contact_info?: Record<string, any> | null;
          address_full?: string | null;
          address_components?: Record<string, any> | null;
          geolocation?: unknown | null;
          opening_hours?: Record<string, any> | null;
          main_image_path?: string | null;
          image_gallery_paths?: string[] | null;
          languages_spoken?: string[] | null;
          services_offered_keywords?: string[] | null;
          expat_friendly_rating?: number | null;
          target_audience_keywords?: string[] | null;
          payment_methods_accepted?: string[] | null;
          wifi_availability?: string | null;
          pet_friendly?: boolean | null;
          kid_friendly?: boolean | null;
          accessibility_features?: string[] | null;
          price_range?: string | null;
          special_offers_for_expats?: string | null;
          owner_is_expat?: boolean | null;
          years_in_business_in_city?: number | null;
          certifications_licenses?: string[] | null;
          testimonials_quotes_crawled?: Record<string, any> | null;
          city_id?: string;
          owner_user_id?: string | null;
          is_verified_expatslist?: boolean;
          claimed_by_user_id?: string | null;
          listing_status?: string;
          source_urls_crawled?: string[] | null;
          last_crawled_at?: string | null;
          is_pinned?: boolean;
          pinned_until?: string | null;
          view_count?: number;
          click_through_count_website?: number;
          click_through_count_phone?: number;
          thumbs_up_count?: number;
          thumbs_down_count?: number;
          last_updated_by_owner_at?: string | null;
          last_updated_by_admin_at?: string | null;
          admin_notes?: string | null;
          created_at?: string;
          updated_at?: string;
          deleted_at?: string | null;
        };
      };
    };
  };
}

export type City = Database['public']['Tables']['cities']['Row'];
export type Category = Database['public']['Tables']['categories']['Row'];
export type Listing = Database['public']['Tables']['listings']['Row'];
