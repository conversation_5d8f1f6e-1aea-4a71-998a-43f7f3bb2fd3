---
// Simple test page to verify middleware is working
const hostname = Astro.url.hostname;
const pathname = Astro.url.pathname;
const headers = Object.fromEntries(Astro.request.headers.entries());
---

<html>
<head>
    <title>Middleware Test</title>
</head>
<body>
    <h1>Middleware Test Page</h1>
    <p><strong>Hostname:</strong> {hostname}</p>
    <p><strong>Pathname:</strong> {pathname}</p>
    <p><strong>Full URL:</strong> {Astro.url.toString()}</p>

    <h2>Headers:</h2>
    <pre>{JSON.stringify(headers, null, 2)}</pre>

    <h2>Test Links:</h2>
    <ul>
        <li><a href="https://expatslist.org/test-middleware">Main domain test</a></li>
        <li><a href="https://playadelcarmen.expatslist.org/test-middleware">Subdomain test</a></li>
        <li><a href="https://expatslist.org/city/playadelcarmen">Direct city page</a></li>
    </ul>
</body>
</html>
