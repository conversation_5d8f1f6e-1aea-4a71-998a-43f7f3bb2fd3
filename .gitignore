# build output
dist/

# generated types
.astro/

# dependencies
node_modules/

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# environment variables
.env
.env.production

# macOS-specific files
.DS_Store

# jetbrains setting folder
.idea/

# VS Code installation files (if accidentally mixed in)
Code.exe
Code.VisualElementsManifest.xml
LICENSES.chromium.html
bin/
chrome_*.pak
d3dcompiler_47.dll
ffmpeg.dll
icudtl.dat
libEGL.dll
libGLESv2.dll
locales/
policies/
resources.pak
resources/
snapshot_blob.bin
tools/
unins000.*
v8_context_snapshot.bin
vk_swiftshader.*
vulkan-1.dll
