---
import Layout from '../layouts/Layout.astro';
import { getCities } from '../lib/database';

// Country data with flag codes and colors
const countryData = {
  'Mexico': { flagCode: 'mx', color: 'bg-green-50 border-green-200 hover:bg-green-100' },
  'Costa Rica': { flagCode: 'cr', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Panama': { flagCode: 'pa', color: 'bg-red-50 border-red-200 hover:bg-red-100' },
  'Colombia': { flagCode: 'co', color: 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100' },
  'Ecuador': { flagCode: 'ec', color: 'bg-purple-50 border-purple-200 hover:bg-purple-100' },
  'Peru': { flagCode: 'pe', color: 'bg-orange-50 border-orange-200 hover:bg-orange-100' },
  'Argentina': { flagCode: 'ar', color: 'bg-cyan-50 border-cyan-200 hover:bg-cyan-100' },
  'Chile': { flagCode: 'cl', color: 'bg-indigo-50 border-indigo-200 hover:bg-indigo-100' },
  'Portugal': { flagCode: 'pt', color: 'bg-emerald-50 border-emerald-200 hover:bg-emerald-100' },
  'Spain': { flagCode: 'es', color: 'bg-rose-50 border-rose-200 hover:bg-rose-100' },
  'Thailand': { flagCode: 'th', color: 'bg-pink-50 border-pink-200 hover:bg-pink-100' },
  'Philippines': { flagCode: 'ph', color: 'bg-teal-50 border-teal-200 hover:bg-teal-100' },
  'Malaysia': { flagCode: 'my', color: 'bg-amber-50 border-amber-200 hover:bg-amber-100' },
  'Czech Republic': { flagCode: 'cz', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Germany': { flagCode: 'de', color: 'bg-gray-50 border-gray-200 hover:bg-gray-100' },
  'Hungary': { flagCode: 'hu', color: 'bg-red-50 border-red-200 hover:bg-red-100' },
  'Brazil': { flagCode: 'br', color: 'bg-green-50 border-green-200 hover:bg-green-100' },
  'Guatemala': { flagCode: 'gt', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Nicaragua': { flagCode: 'ni', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Honduras': { flagCode: 'hn', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'El Salvador': { flagCode: 'sv', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' },
  'Belize': { flagCode: 'bz', color: 'bg-blue-50 border-blue-200 hover:bg-blue-100' }
};

// Get all cities organized by country, with Mexico first
let allCities = null;
let debugInfo = '';

try {
  const { data, error } = await getCities();

  if (error) {
    console.error('Database error:', error);
  } else {
    allCities = data;
    console.log('Cities loaded successfully:', data?.length);
  }
} catch (e) {
  console.error('Database connection error:', e);
}

// Organize cities by country with Mexico first
const citiesByCountry: Record<string, any[]> = {};
if (allCities) {
  allCities.forEach((city: any) => {
    if (!citiesByCountry[city.country]) {
      citiesByCountry[city.country] = [];
    }
    citiesByCountry[city.country].push(city);
  });
}

// Sort countries with Mexico first
const sortedCountries = Object.keys(citiesByCountry).sort((a, b) => {
  if (a === 'Mexico') return -1;
  if (b === 'Mexico') return 1;
  return a.localeCompare(b);
});

const pageTitle = 'ExpatsList - Global Directory';
const pageDescription = 'Global directory of local businesses and services for expats worldwide.';
---

<Layout title={pageTitle} description={pageDescription}>
  <main class="min-h-screen bg-gray-50">
    <!-- Hero Section with Search -->
    <div class="bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div class="max-w-5xl mx-auto px-6 py-16">
        <div class="text-center mb-10">
          <!-- Dismissible Expat Definition Card -->
          <div id="expat-definition" class="max-w-2xl mx-auto mb-8 hidden">
            <div class="bg-blue-50 border border-blue-200 rounded-2xl p-6 shadow-sm relative">
              <button
                onclick="dismissDefinition()"
                class="absolute top-3 right-3 text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="Close definition"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
              <div class="flex items-center justify-center mb-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                <span class="text-sm font-medium text-blue-700 uppercase tracking-wide">What's an Expat?</span>
              </div>
              <p class="text-blue-800 leading-relaxed pr-8">
                An <strong>expat</strong> (expatriate) is someone living temporarily outside their home country, often for work, study, or lifestyle reasons
              </p>
            </div>
          </div>

          <!-- Value Proposition with Expat Info Button -->
          <div class="mb-8">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-2 leading-tight max-w-4xl mx-auto">
              Find trusted local businesses
              <span class="block text-blue-600 mt-2">recommended by fellow expats</span>
            </h1>
            <button
              id="expat-info-btn"
              onclick="showDefinition()"
              class="inline-flex items-center text-sm text-gray-500 hover:text-blue-600 transition-colors group"
            >
              <svg class="w-4 h-4 mr-1 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              What's an expat?
            </button>
          </div>
        </div>

        <script is:inline>
          // Global functions for the definition modal
          window.dismissDefinition = function() {
            const definition = document.getElementById('expat-definition');
            const button = document.getElementById('expat-info-btn');

            if (definition && button) {
              definition.classList.add('hidden');
              button.style.display = 'inline-flex';
              localStorage.setItem('expatDefinitionSeen', 'true');
            }
          };

          window.showDefinition = function() {
            const definition = document.getElementById('expat-definition');
            const button = document.getElementById('expat-info-btn');

            if (definition && button) {
              definition.classList.remove('hidden');
              button.style.display = 'none';
            }
          };

          // Check if user has seen the definition before
          function checkFirstVisit() {
            const hasSeenDefinition = localStorage.getItem('expatDefinitionSeen');
            const definition = document.getElementById('expat-definition');
            const button = document.getElementById('expat-info-btn');

            if (!hasSeenDefinition && definition && button) {
              definition.classList.remove('hidden');
              button.style.display = 'none';
            }
          }

          // Run on page load
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', checkFirstVisit);
          } else {
            checkFirstVisit();
          }
        </script>

        <!-- Optimized Search Section -->
        <div class="max-w-4xl mx-auto">
          <!-- Search Box -->
          <div class="bg-white rounded-3xl shadow-xl border border-gray-100 p-2 mb-8">
            <form action="/search" method="GET" class="flex items-center">
              <div class="flex-1 relative">
                <div class="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  name="q"
                  placeholder="Best coffee shop in Playa del Carmen..."
                  class="w-full pl-14 pr-4 py-5 text-lg bg-transparent border-0 focus:outline-none placeholder-gray-500"
                />
              </div>
              <button
                type="submit"
                class="flex-shrink-0 bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-5 rounded-2xl transition-colors focus:outline-none focus:ring-4 focus:ring-blue-100"
              >
                Search
              </button>
            </form>
          </div>

          <!-- Quick Search Suggestions -->
          <div class="text-center">
            <p class="text-sm text-gray-500 mb-4">Popular searches:</p>
            <div class="flex flex-wrap justify-center gap-3">
              <button
                type="button"
                class="bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-200 text-gray-700 hover:text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-all shadow-sm hover:shadow-md"
                onclick="document.querySelector('input[name=q]').value='English-speaking doctors'; document.querySelector('form').submit();"
              >
                🩺 English-speaking doctors
              </button>
              <button
                type="button"
                class="bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-200 text-gray-700 hover:text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-all shadow-sm hover:shadow-md"
                onclick="document.querySelector('input[name=q]').value='Best restaurants'; document.querySelector('form').submit();"
              >
                🍽️ Best restaurants
              </button>
              <button
                type="button"
                class="bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-200 text-gray-700 hover:text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-all shadow-sm hover:shadow-md"
                onclick="document.querySelector('input[name=q]').value='Coworking spaces'; document.querySelector('form').submit();"
              >
                💻 Coworking spaces
              </button>
              <button
                type="button"
                class="bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-200 text-gray-700 hover:text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-all shadow-sm hover:shadow-md"
                onclick="document.querySelector('input[name=q]').value='Tax advisors'; document.querySelector('form').submit();"
              >
                📊 Tax advisors
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cities Section -->
    <div class="bg-gradient-to-b from-gray-50 to-white py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Popular Expat Destinations</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover trusted recommendations from expat communities around the world
          </p>


        </div>

        {sortedCountries.map((country) => (
          <div class="mb-16">
            <div class="flex items-center justify-center mb-8">
              <span class={`fi fi-${(countryData as any)[country]?.flagCode || 'un'} mr-4`} style="font-size: 2.5rem; line-height: 1;"></span>
              <h3 class="text-2xl font-bold text-gray-900">
                {country}
              </h3>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {citiesByCountry[country].map((city: any) => {
                // All cities now get subdomain links
                return (
                  <a
                    href={`https://${city.subdomain_slug}.expatslist.org`}
                    class="bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-300 p-6 rounded-2xl transition-all text-center group shadow-sm hover:shadow-lg transform hover:-translate-y-1"
                  >
                    <div class="font-semibold text-gray-900 mb-2 group-hover:text-blue-700 text-lg">{city.name}</div>
                    {city.total_listing_count > 0 ? (
                      <div class="text-sm text-gray-500 bg-gray-100 group-hover:bg-blue-100 px-3 py-1 rounded-full inline-block">
                        {city.total_listing_count} recommendations
                      </div>
                    ) : (
                      <div class="text-sm text-gray-400 bg-gray-200 px-3 py-1 rounded-full inline-block">
                        Coming soon
                      </div>
                    )}
                  </a>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <p class="text-gray-400 text-xs">
            © 2025 ExpatsList LLC. Web development by
            <a href="https://vibe8.app" target="_blank" class="text-blue-400 hover:text-blue-300 transition-colors ml-1">
              Vibe8.app
            </a>
          </p>
        </div>
      </div>
    </footer>
  </main>
</Layout>
