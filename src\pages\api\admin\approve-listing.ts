import type { APIRoute } from 'astro';
import { supabase } from '../../../lib/supabase';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { listingId } = await request.json();

    if (!listingId) {
      return new Response(JSON.stringify({ error: 'Missing listing ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Update listing status to active
    const { error } = await supabase
      .from('listings')
      .update({ 
        listing_status: 'active',
        last_updated_by_admin_at: new Date().toISOString()
      })
      .eq('id', listingId);

    if (error) {
      console.error('Error approving listing:', error);
      return new Response(JSON.stringify({ error: 'Failed to approve listing' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
