---
// Path-based business page: /playa-del-carmen/business-name
import Layout from '../../layouts/Layout.astro';
import { getCityBySlug } from '../../lib/database';
import { supabase } from '../../lib/supabase';

const { city: citySlug, business: businessSlug } = Astro.params;

if (!citySlug || !businessSlug) {
  return Astro.redirect('/cities');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);

if (cityError || !city) {
  return Astro.redirect('/cities');
}

// Get business listing
const { data: listing, error: listingError } = await supabase
  .from('listings')
  .select(`
    *,
    categories!inner(name, icon_slug, description)
  `)
  .eq('city_id', city.id)
  .eq('slug', businessSlug)
  .eq('listing_status', 'active')
  .is('deleted_at', null)
  .single();

if (listingError || !listing) {
  return Astro.redirect(`/${citySlug}`);
}

// Increment view count (fire and forget)
supabase
  .from('listings')
  .update({ view_count: (listing.view_count || 0) + 1 })
  .eq('id', listing.id)
  .then(() => {});

const pageTitle = `${listing.display_name || listing.business_name} - ${listing.categories.name} in ${city.name}`;
const pageDescription = listing.description_short || `${listing.display_name || listing.business_name} in ${city.name}. ${listing.categories.description || ''}`;
const canonicalUrl = `https://expatslist.org/${citySlug}/${businessSlug}`;
---

<Layout title={pageTitle} description={pageDescription} canonical={canonicalUrl}>
  <main class="min-h-screen bg-gray-50">
    <!-- Breadcrumb Navigation -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 py-3">
        <nav class="flex items-center space-x-2 text-sm text-gray-600">
          <a href="/" class="hover:text-blue-600 transition-colors">Home</a>
          <span>›</span>
          <a href="/cities" class="hover:text-blue-600 transition-colors">Cities</a>
          <span>›</span>
          <a href={`/${citySlug}`} class="hover:text-blue-600 transition-colors">{city.name}</a>
          <span>›</span>
          <a href={`/${citySlug}/${listing.categories.slug || listing.category_primary_id}`} class="hover:text-blue-600 transition-colors">{listing.categories.name}</a>
          <span>›</span>
          <span class="text-gray-900 font-medium">{listing.display_name || listing.business_name}</span>
        </nav>
      </div>
    </div>

    <!-- Business Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 py-8">
        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
          <!-- Business Info -->
          <div class="flex-1">
            <div class="flex items-start gap-4 mb-4">
              <!-- Business Icon/Image -->
              <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                <span class="text-2xl">{listing.categories.icon_slug || '🏪'}</span>
              </div>
              
              <!-- Business Details -->
              <div class="flex-1">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                  {listing.display_name || listing.business_name}
                </h1>
                <div class="flex items-center gap-4 text-sm text-gray-600 mb-3">
                  <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">
                    {listing.categories.name}
                  </span>
                  {listing.is_verified_expatslist && (
                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">
                      ✓ Verified by ExpatsList
                    </span>
                  )}
                  {listing.is_pinned && (
                    <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full font-medium">
                      📌 Featured
                    </span>
                  )}
                </div>
                
                <!-- Short Description -->
                {listing.description_short && (
                  <p class="text-gray-700 text-lg leading-relaxed">
                    {listing.description_short}
                  </p>
                )}
              </div>
            </div>

            <!-- Key Features -->
            <div class="flex flex-wrap gap-4 text-sm">
              {listing.languages_spoken && listing.languages_spoken.includes('English') && (
                <div class="flex items-center text-green-700 bg-green-50 px-3 py-2 rounded-lg">
                  <span class="mr-2">🇺🇸</span>
                  English Speaking
                </div>
              )}
              {listing.expat_friendly_rating && (
                <div class="flex items-center text-yellow-700 bg-yellow-50 px-3 py-2 rounded-lg">
                  <span class="mr-2">⭐</span>
                  {listing.expat_friendly_rating}/5 Expat Rating
                </div>
              )}
              {listing.owner_is_expat && (
                <div class="flex items-center text-blue-700 bg-blue-50 px-3 py-2 rounded-lg">
                  <span class="mr-2">👤</span>
                  Expat Owned
                </div>
              )}
              {listing.pet_friendly && (
                <div class="flex items-center text-purple-700 bg-purple-50 px-3 py-2 rounded-lg">
                  <span class="mr-2">🐕</span>
                  Pet Friendly
                </div>
              )}
            </div>
          </div>

          <!-- Contact Actions -->
          <div class="lg:w-80">
            <div class="bg-gray-50 rounded-xl p-6">
              <h3 class="font-semibold text-gray-900 mb-4">Contact Information</h3>
              
              <!-- Action Buttons -->
              <div class="space-y-3 mb-6">
                {listing.contact_info?.website_url && (
                  <a 
                    href={listing.contact_info.website_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    onclick="trackClick('website')"
                    class="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                    Visit Website
                  </a>
                )}
                
                {listing.contact_info?.phone && (
                  <a 
                    href={`tel:${listing.contact_info.phone}`}
                    onclick="trackClick('phone')"
                    class="w-full inline-flex items-center justify-center px-4 py-3 border border-blue-600 text-sm font-medium rounded-lg text-blue-600 bg-white hover:bg-blue-50 transition-colors"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    {listing.contact_info.phone}
                  </a>
                )}
                
                {listing.contact_info?.email && (
                  <a 
                    href={`mailto:${listing.contact_info.email}`}
                    class="w-full inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    Send Email
                  </a>
                )}
              </div>

              <!-- Address -->
              {listing.address_full && (
                <div class="mb-4">
                  <h4 class="font-medium text-gray-900 mb-2">Address</h4>
                  <p class="text-gray-700 text-sm">{listing.address_full}</p>
                </div>
              )}

              <!-- Opening Hours -->
              {listing.opening_hours && (
                <div>
                  <h4 class="font-medium text-gray-900 mb-2">Opening Hours</h4>
                  <div class="text-sm text-gray-700 space-y-1">
                    {Object.entries(listing.opening_hours).map(([day, hours]) => (
                      <div class="flex justify-between">
                        <span class="capitalize">{day}:</span>
                        <span>{hours || 'Closed'}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Business Details -->
    <div class="max-w-7xl mx-auto px-4 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
          <!-- Description -->
          {listing.description_long && (
            <div class="bg-white rounded-xl shadow-sm p-8">
              <h2 class="text-2xl font-bold text-gray-900 mb-6">About {listing.display_name || listing.business_name}</h2>
              <div class="prose prose-gray max-w-none">
                <p class="text-gray-700 leading-relaxed whitespace-pre-line">{listing.description_long}</p>
              </div>
            </div>
          )}

          <!-- Services -->
          {listing.services_offered_keywords && listing.services_offered_keywords.length > 0 && (
            <div class="bg-white rounded-xl shadow-sm p-8">
              <h2 class="text-2xl font-bold text-gray-900 mb-6">Services Offered</h2>
              <div class="flex flex-wrap gap-2">
                {listing.services_offered_keywords.map((service) => (
                  <span class="bg-blue-50 text-blue-700 px-3 py-2 rounded-lg text-sm font-medium">
                    {service}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Quick Actions -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="font-bold text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-3">
              <button
                onclick="thumbsUp()"
                class="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-green-700 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
              >
                👍 Recommend ({listing.thumbs_up_count || 0})
              </button>
              <button
                onclick="reportIssue()"
                class="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
              >
                🚩 Report Issue
              </button>
            </div>
          </div>

          <!-- Related Businesses -->
          <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="font-bold text-gray-900 mb-4">More in {listing.categories.name}</h3>
            <a
              href={`/${citySlug}/${listing.categories.slug || listing.category_primary_id}`}
              class="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              View all {listing.categories.name} →
            </a>
          </div>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script>
  function trackClick(type: string) {
    // Track click analytics
    fetch('/api/track-click', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        listing_id: '{listing.id}',
        click_type: type
      })
    }).catch(() => {});
  }

  function thumbsUp() {
    // Handle thumbs up
    fetch('/api/thumbs-up', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ listing_id: '{listing.id}' })
    }).then(() => {
      alert('Thank you for your recommendation!');
    }).catch(() => {
      alert('Please try again later.');
    });
  }

  function reportIssue() {
    const reason = prompt('Please tell us what\'s wrong:');
    if (reason) {
      fetch('/api/report-issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          listing_id: '{listing.id}',
          reason: reason
        })
      }).then(() => {
        alert('Thank you for reporting this issue. We\'ll review it shortly.');
      }).catch(() => {
        alert('Please try again later.');
      });
    }
  }
</script>
