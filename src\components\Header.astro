---
import { extractSubdomain } from '../lib/utils';
import type { City } from '../lib/supabase';

export interface Props {
  city?: City | null;
  showCitySelector?: boolean;
}

const { city, showCitySelector = true } = Astro.props;
const subdomain = extractSubdomain(Astro.url.hostname);
const isMainSite = !subdomain;
---

<header class="bg-white shadow-sm border-b border-gray-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex items-center">
        <a href={isMainSite ? "/" : `https://expatslist.org`} class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-sm">EL</span>
          </div>
          <div class="flex flex-col">
            <span class="text-xl font-bold text-gray-900">ExpatsList</span>
            {city && (
              <span class="text-sm text-gray-500 -mt-1">{city.name}</span>
            )}
          </div>
        </a>
      </div>

      <!-- Navigation -->
      <nav class="hidden md:flex items-center space-x-8">
        {!isMainSite && (
          <>
            <a href="/" class="text-gray-700 hover:text-blue-600 font-medium">Browse</a>
            <a href="/categories" class="text-gray-700 hover:text-blue-600 font-medium">Categories</a>
            <a href="/search" class="text-gray-700 hover:text-blue-600 font-medium">Search</a>
          </>
        )}
        <a href="/list-business" class="text-gray-700 hover:text-blue-600 font-medium">List Your Business</a>
        <a href="/about" class="text-gray-700 hover:text-blue-600 font-medium">About</a>
      </nav>

      <!-- City Selector & Actions -->
      <div class="flex items-center space-x-4">
        {showCitySelector && (
          <div class="relative">
            <button 
              id="city-selector-btn"
              class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span>{city ? city.name : 'Choose City'}</span>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            <!-- City Dropdown (will be populated by JavaScript) -->
            <div 
              id="city-dropdown" 
              class="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50 hidden"
            >
              <div class="p-3 border-b border-gray-200">
                <input 
                  type="text" 
                  id="city-search"
                  placeholder="Search cities..."
                  class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div id="city-list" class="max-h-64 overflow-y-auto">
                <!-- Cities will be loaded here -->
              </div>
            </div>
          </div>
        )}

        <!-- Auth Buttons -->
        <div class="flex items-center space-x-2">
          <button class="px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600">
            Sign In
          </button>
          <button class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
            Sign Up
          </button>
        </div>

        <!-- Mobile Menu Button -->
        <button 
          id="mobile-menu-btn"
          class="md:hidden p-2 text-gray-700 hover:text-blue-600"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="md:hidden hidden border-t border-gray-200 py-4">
      <div class="flex flex-col space-y-3">
        {!isMainSite && (
          <>
            <a href="/" class="text-gray-700 hover:text-blue-600 font-medium">Browse</a>
            <a href="/categories" class="text-gray-700 hover:text-blue-600 font-medium">Categories</a>
            <a href="/search" class="text-gray-700 hover:text-blue-600 font-medium">Search</a>
          </>
        )}
        <a href="/list-business" class="text-gray-700 hover:text-blue-600 font-medium">List Your Business</a>
        <a href="/about" class="text-gray-700 hover:text-blue-600 font-medium">About</a>
      </div>
    </div>
  </div>
</header>

<script>
  // City selector functionality
  document.addEventListener('DOMContentLoaded', () => {
    const cityBtn = document.getElementById('city-selector-btn');
    const cityDropdown = document.getElementById('city-dropdown');
    const citySearch = document.getElementById('city-search');
    const cityList = document.getElementById('city-list');
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');

    // Mobile menu toggle
    if (mobileMenuBtn && mobileMenu) {
      mobileMenuBtn.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
    }

    // City selector toggle
    if (cityBtn && cityDropdown) {
      cityBtn.addEventListener('click', async () => {
        cityDropdown.classList.toggle('hidden');
        
        if (!cityDropdown.classList.contains('hidden')) {
          // Load cities if not already loaded
          if (cityList && cityList.children.length === 0) {
            await loadCities();
          }
          // Focus search input
          if (citySearch) {
            citySearch.focus();
          }
        }
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        if (!cityBtn.contains(e.target as Node) && !cityDropdown.contains(e.target as Node)) {
          cityDropdown.classList.add('hidden');
        }
      });
    }

    // City search functionality
    if (citySearch) {
      citySearch.addEventListener('input', (e) => {
        const searchTerm = (e.target as HTMLInputElement).value.toLowerCase();
        const cityItems = cityList?.querySelectorAll('[data-city]');
        
        cityItems?.forEach((item) => {
          const cityName = item.getAttribute('data-city')?.toLowerCase() || '';
          if (cityName.includes(searchTerm)) {
            (item as HTMLElement).style.display = 'block';
          } else {
            (item as HTMLElement).style.display = 'none';
          }
        });
      });
    }

    async function loadCities() {
      try {
        const response = await fetch('/api/cities');
        const cities = await response.json();
        
        if (cityList) {
          cityList.innerHTML = cities.map((city: any) => `
            <a 
              href="https://${city.subdomain_slug}.expatslist.org"
              data-city="${city.name}"
              class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 border-b border-gray-100 last:border-b-0"
            >
              <div class="font-medium">${city.name}</div>
              <div class="text-xs text-gray-500">${city.country || ''}</div>
            </a>
          `).join('');
        }
      } catch (error) {
        console.error('Failed to load cities:', error);
        if (cityList) {
          cityList.innerHTML = '<div class="px-4 py-2 text-sm text-gray-500">Failed to load cities</div>';
        }
      }
    }
  });
</script>
