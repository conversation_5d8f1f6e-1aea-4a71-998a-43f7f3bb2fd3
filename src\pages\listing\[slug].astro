---
import Layout from '../../layouts/Layout.astro';
import Header from '../../components/Header.astro';
import { extractSubdomain, getCityBySubdomain, formatOpeningHours, formatPriceRange } from '../../lib/utils';
import { supabase } from '../../lib/supabase';

export const prerender = false; // This page needs to be server-rendered

// Get city from subdomain
const subdomain = extractSubdomain(Astro.url.hostname);
let city = null;

if (subdomain) {
  city = await getCityBySubdomain(subdomain);
  if (!city) {
    return Astro.redirect('https://expatslist.org');
  }
} else {
  return Astro.redirect('https://expatslist.org');
}

// Get listing slug from URL
const { slug } = Astro.params;

// Fetch listing details
const { data: listing, error: listingError } = await supabase
  .from('listings')
  .select(`
    *,
    categories!inner(name, icon_slug, description)
  `)
  .eq('city_id', city.id)
  .eq('slug', slug)
  .eq('listing_status', 'active')
  .is('deleted_at', null)
  .single();

if (listingError || !listing) {
  return Astro.redirect('/');
}

// Increment view count (fire and forget)
supabase
  .from('listings')
  .update({ view_count: listing.view_count + 1 })
  .eq('id', listing.id)
  .then(() => {});

const pageTitle = `${listing.display_name || listing.business_name} - ${listing.categories.name}`;
const pageDescription = listing.description_short || `${listing.display_name || listing.business_name} in ${city.name}. ${listing.categories.description || ''}`;
---

<Layout title={pageTitle} description={pageDescription} city={city.name}>
  <Header city={city} />
  
  <main class="min-h-screen bg-gray-50">
    <!-- Breadcrumb -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav class="flex items-center space-x-2 text-sm text-gray-600">
          <a href="/" class="hover:text-blue-600">{city.name}</a>
          <span>›</span>
          <a href={`/category/${listing.categories.id}`} class="hover:text-blue-600">{listing.categories.name}</a>
          <span>›</span>
          <span class="text-gray-900">{listing.display_name || listing.business_name}</span>
        </nav>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
          <!-- Header -->
          <div class="bg-white rounded-xl shadow-md p-6 mb-6">
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                  {listing.display_name || listing.business_name}
                </h1>
                <div class="flex items-center space-x-4 mb-4">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {listing.categories.name}
                  </span>
                  {listing.is_verified_expatslist && (
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                      ✓ Verified
                    </span>
                  )}
                  {listing.is_pinned && (
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                      ⭐ Featured
                    </span>
                  )}
                </div>
              </div>
            </div>

            {listing.description_short && (
              <p class="text-lg text-gray-700 mb-4">{listing.description_short}</p>
            )}

            <!-- Tags -->
            <div class="flex flex-wrap gap-2 mb-6">
              {listing.price_range && (
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {formatPriceRange(listing.price_range)}
                </span>
              )}
              {listing.languages_spoken && listing.languages_spoken.map((lang) => (
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {lang}
                </span>
              ))}
              {listing.owner_is_expat && (
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Expat-owned
                </span>
              )}
              {listing.pet_friendly && (
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Pet-friendly
                </span>
              )}
              {listing.kid_friendly && (
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                  Kid-friendly
                </span>
              )}
              {listing.wifi_availability && listing.wifi_availability !== 'None' && (
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                  WiFi: {listing.wifi_availability}
                </span>
              )}
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-3">
              {listing.contact_info?.website_url && (
                <a 
                  href={listing.contact_info.website_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  onclick="trackClick('website')"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  Visit Website
                </a>
              )}
              {listing.contact_info?.phone && (
                <a 
                  href={`tel:${listing.contact_info.phone}`}
                  onclick="trackClick('phone')"
                  class="inline-flex items-center px-4 py-2 border border-blue-600 text-sm font-medium rounded-lg text-blue-600 bg-white hover:bg-blue-50 transition-colors"
                >
                  📞 {listing.contact_info.phone}
                </a>
              )}
              {listing.contact_info?.email && (
                <a 
                  href={`mailto:${listing.contact_info.email}`}
                  class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  ✉️ Email
                </a>
              )}
            </div>
          </div>

          <!-- Description -->
          {listing.description_long && (
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-4">About</h2>
              <div class="prose prose-gray max-w-none">
                <p class="text-gray-700 whitespace-pre-line">{listing.description_long}</p>
              </div>
            </div>
          )}

          <!-- Services -->
          {listing.services_offered_keywords && listing.services_offered_keywords.length > 0 && (
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-4">Services Offered</h2>
              <div class="flex flex-wrap gap-2">
                {listing.services_offered_keywords.map((service) => (
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                    {service}
                  </span>
                ))}
              </div>
            </div>
          )}

          <!-- Special Offers -->
          {listing.special_offers_for_expats && (
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-4">Special Offers for Expats</h2>
              <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <p class="text-green-800">{listing.special_offers_for_expats}</p>
              </div>
            </div>
          )}
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
          <!-- Contact Info -->
          <div class="bg-white rounded-xl shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
            
            {listing.address_full && (
              <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-1">Address</h4>
                <p class="text-gray-900">{listing.address_full}</p>
              </div>
            )}

            {listing.contact_info?.phone && (
              <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-1">Phone</h4>
                <a href={`tel:${listing.contact_info.phone}`} class="text-blue-600 hover:text-blue-700">
                  {listing.contact_info.phone}
                </a>
              </div>
            )}

            {listing.contact_info?.email && (
              <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-1">Email</h4>
                <a href={`mailto:${listing.contact_info.email}`} class="text-blue-600 hover:text-blue-700">
                  {listing.contact_info.email}
                </a>
              </div>
            )}

            {listing.opening_hours && (
              <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-1">Hours</h4>
                <p class="text-gray-900">{formatOpeningHours(listing.opening_hours)}</p>
              </div>
            )}
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-xl shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            
            <!-- Thumbs up/down -->
            <div class="flex items-center space-x-4 mb-4">
              <button 
                onclick="vote('up')"
                class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-green-100 hover:text-green-700 transition-colors"
              >
                <span>👍</span>
                <span id="thumbs-up-count">{listing.thumbs_up_count}</span>
              </button>
              <button 
                onclick="vote('down')"
                class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-red-100 hover:text-red-700 transition-colors"
              >
                <span>👎</span>
                <span id="thumbs-down-count">{listing.thumbs_down_count}</span>
              </button>
            </div>

            <!-- Share -->
            <button 
              onclick="shareListng()"
              class="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Share this listing
            </button>
          </div>

          <!-- Business Stats -->
          <div class="bg-white rounded-xl shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Business Info</h3>
            
            {listing.years_in_business_in_city && (
              <div class="mb-3">
                <span class="text-sm text-gray-600">Years in {city.name}:</span>
                <span class="ml-2 font-medium text-gray-900">{listing.years_in_business_in_city}</span>
              </div>
            )}
            
            <div class="mb-3">
              <span class="text-sm text-gray-600">Views:</span>
              <span class="ml-2 font-medium text-gray-900">{listing.view_count + 1}</span>
            </div>

            {listing.payment_methods_accepted && listing.payment_methods_accepted.length > 0 && (
              <div>
                <span class="text-sm text-gray-600 block mb-1">Payment Methods:</span>
                <div class="flex flex-wrap gap-1">
                  {listing.payment_methods_accepted.map((method) => (
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                      {method}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script define:vars={{ listingId: listing.id }}>
  function trackClick(type) {
    // Track click-through for analytics
    fetch('/api/track-click', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        listingId: listingId,
        type: type
      })
    }).catch(console.error);
  }

  function vote(direction) {
    // Handle thumbs up/down voting
    fetch('/api/vote', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        listingId: listingId,
        direction: direction
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        document.getElementById('thumbs-up-count').textContent = data.thumbsUp;
        document.getElementById('thumbs-down-count').textContent = data.thumbsDown;
      }
    })
    .catch(console.error);
  }

  function shareListng() {
    if (navigator.share) {
      navigator.share({
        title: document.title,
        url: window.location.href
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href).then(() => {
        alert('Link copied to clipboard!');
      });
    }
  }
</script>
