---
// Path-based city homepage: /playa-del-carmen/
import Layout from '../../layouts/Layout.astro';
import { getCityBySlug, getListingsForCity, getCategories } from '../../lib/database';

const { city: citySlug } = Astro.params;

if (!citySlug) {
  return Astro.redirect('/cities');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);

if (cityError || !city) {
  return Astro.redirect('/cities');
}

// Get listings and categories
const [
  { data: listings },
  { data: categories }
] = await Promise.all([
  getListingsForCity(city.id),
  getCategories()
]);

const pageTitle = `${city.name} - Local Businesses for Expats`;
const pageDescription = `Discover trusted local businesses in ${city.name}. Find restaurants, services, and more recommended by fellow expats.`;
const canonicalUrl = `https://expatslist.org/${citySlug}`;
---

<Layout title={pageTitle} description={pageDescription} canonical={canonicalUrl}>
  <main class="min-h-screen bg-gray-50">
    <!-- Breadcrumb Navigation -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 py-3">
        <nav class="flex items-center space-x-2 text-sm text-gray-600">
          <a href="/" class="hover:text-blue-600 transition-colors">Home</a>
          <span>›</span>
          <a href="/cities" class="hover:text-blue-600 transition-colors">Cities</a>
          <span>›</span>
          <span class="text-gray-900 font-medium">{city.name}</span>
        </nav>
      </div>
    </div>

    <!-- Hero Section -->
    <div class="bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div class="max-w-7xl mx-auto px-4 py-16">
        <div class="text-center">
          <!-- City Header -->
          <div class="mb-8">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {city.name}
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
              Find trusted local businesses and services recommended by fellow expats
            </p>
          </div>

          <!-- Quick Search -->
          <div class="max-w-2xl mx-auto">
            <form action={`/${citySlug}/search`} method="GET" class="bg-white rounded-2xl shadow-lg border border-gray-100 p-2">
              <div class="flex items-center">
                <div class="flex-1 relative">
                  <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    name="q"
                    placeholder={`Search businesses in ${city.name}...`}
                    class="w-full pl-12 pr-4 py-4 text-lg bg-transparent border-0 focus:outline-none placeholder-gray-500"
                  />
                </div>
                <button
                  type="submit"
                  class="flex-shrink-0 bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-xl transition-colors"
                >
                  Search
                </button>
              </div>
            </form>
          </div>

          <!-- Quick Categories -->
          <div class="mt-8">
            <p class="text-sm text-gray-500 mb-4">Popular categories:</p>
            <div class="flex flex-wrap justify-center gap-3">
              <a href={`/${citySlug}/restaurants`} class="bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-200 text-gray-700 hover:text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-all shadow-sm hover:shadow-md">
                🍽️ Restaurants
              </a>
              <a href={`/${citySlug}/healthcare`} class="bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-200 text-gray-700 hover:text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-all shadow-sm hover:shadow-md">
                🏥 Healthcare
              </a>
              <a href={`/${citySlug}/coworking`} class="bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-200 text-gray-700 hover:text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-all shadow-sm hover:shadow-md">
                💻 Coworking
              </a>
              <a href={`/${citySlug}/services`} class="bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-200 text-gray-700 hover:text-blue-700 px-4 py-2 rounded-full text-sm font-medium transition-all shadow-sm hover:shadow-md">
                🔧 Services
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Categories Grid -->
    {categories && categories.length > 0 && (
      <div class="max-w-7xl mx-auto px-4 py-16">
        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Browse All Categories</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6">
          {categories.map((category) => (
            <a
              href={`/${citySlug}/${category.slug || category.id}`}
              class="bg-white hover:bg-blue-50 p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:shadow-lg group"
            >
              <div class="text-center">
                <div class="text-4xl mb-4 group-hover:scale-110 transition-transform">
                  {category.icon_slug || '📁'}
                </div>
                <h3 class="text-sm font-semibold text-gray-900 leading-tight mb-1">
                  {category.name}
                </h3>
                <p class="text-xs text-gray-500">
                  {category.listing_count || 0} businesses
                </p>
              </div>
            </a>
          ))}
        </div>
      </div>
    )}

    <!-- Featured Listings -->
    {listings && listings.length > 0 && (
      <div class="bg-white">
        <div class="max-w-7xl mx-auto px-4 py-16">
          <div class="flex items-center justify-between mb-8">
            <h2 class="text-3xl font-bold text-gray-900">
              Featured Businesses
            </h2>
            <a
              href={`/${citySlug}/all`}
              class="text-blue-600 hover:text-blue-700 font-medium flex items-center"
            >
              View all {listings.length} businesses
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </a>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {listings.slice(0, 9).map((listing) => (
              <div class="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-blue-200">
                <!-- Business Header -->
                <div class="flex items-start justify-between mb-4">
                  <div class="flex-1">
                    <h3 class="font-bold text-lg text-gray-900 mb-1">
                      <a
                        href={`/${citySlug}/${listing.slug}`}
                        class="hover:text-blue-600 transition-colors"
                      >
                        {listing.display_name || listing.business_name}
                      </a>
                    </h3>
                    <p class="text-sm text-blue-600 font-medium">{listing.category_name}</p>
                  </div>
                  
                  <!-- Badges -->
                  <div class="flex flex-col gap-1">
                    {listing.is_verified_expatslist && (
                      <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full font-medium">
                        ✓ Verified
                      </span>
                    )}
                    {listing.is_pinned && (
                      <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full font-medium">
                        📌 Featured
                      </span>
                    )}
                  </div>
                </div>
                
                <!-- Description -->
                {listing.description_short && (
                  <p class="text-gray-700 text-sm mb-4 line-clamp-3">
                    {listing.description_short}
                  </p>
                )}
                
                <!-- Features -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3 text-xs text-gray-600">
                    {listing.languages_spoken && listing.languages_spoken.includes('English') && (
                      <span class="flex items-center">
                        <span class="w-4 h-4 mr-1">🇺🇸</span>
                        English
                      </span>
                    )}
                    {listing.expat_friendly_rating && (
                      <span class="flex items-center">
                        <span class="mr-1">⭐</span>
                        {listing.expat_friendly_rating}/5
                      </span>
                    )}
                  </div>
                  
                  <a
                    href={`/${citySlug}/${listing.slug}`}
                    class="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
                  >
                    View details
                    <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )}

    <!-- Empty State -->
    {(!listings || listings.length === 0) && (
      <div class="bg-white">
        <div class="max-w-4xl mx-auto px-4 py-16 text-center">
          <div class="text-8xl mb-6">🏪</div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">No businesses yet in {city.name}</h2>
          <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Be the first to add a business and help fellow expats discover great local services.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/list-business"
              class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
            >
              Add Your Business
            </a>
            <a
              href="/cities"
              class="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold"
            >
              Browse Other Cities
            </a>
          </div>
        </div>
      </div>
    )}
  </main>
</Layout>

<style>
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
