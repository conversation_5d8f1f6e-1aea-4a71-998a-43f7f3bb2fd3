---
import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import { extractSubdomain, getCityBySubdomain } from '../lib/utils';
import { getCities, getCategories } from '../lib/database';

// Get city from subdomain (optional for this page)
const subdomain = extractSubdomain(Astro.url.hostname);
let city = null;

if (subdomain) {
  city = await getCityBySubdomain(subdomain);
}

// Get all cities for the dropdown
const { data: cities } = await getCities();

// Get all categories
const { data: categories } = await getCategories();

const pageTitle = city ? `List Your Business in ${city.name}` : 'List Your Business on ExpatsList';
const pageDescription = 'Get your business in front of thousands of expats. Join ExpatsList and connect with your target audience.';
---

<Layout title={pageTitle} description={pageDescription} city={city?.name}>
  <Header city={city} />

  <main class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-br from-blue-50 to-indigo-100">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            List Your Business on <span class="text-blue-600">ExpatsList</span>
          </h1>
          <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Connect with thousands of expats looking for trusted local services.
            Get verified and stand out from the competition.
          </p>

          <!-- Benefits -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mb-12">
            <div class="bg-white rounded-xl p-6 shadow-md">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-2xl">🎯</span>
              </div>
              <h3 class="font-semibold text-gray-900 mb-2">Targeted Audience</h3>
              <p class="text-sm text-gray-600">Reach expats who are actively looking for services like yours</p>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-md">
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-2xl">✅</span>
              </div>
              <h3 class="font-semibold text-gray-900 mb-2">Get Verified</h3>
              <p class="text-sm text-gray-600">Build trust with a verified badge and professional listing</p>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-md">
              <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-2xl">📈</span>
              </div>
              <h3 class="font-semibold text-gray-900 mb-2">Grow Your Business</h3>
              <p class="text-sm text-gray-600">Increase visibility and attract new customers</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="bg-white rounded-xl shadow-lg p-8">
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-gray-900 mb-2">Submit Your Business</h2>
          <p class="text-gray-600">Fill out the form below to get your business listed. We'll review your submission and get back to you within 24 hours.</p>
        </div>

        <form id="business-form" class="space-y-6">
          <!-- Basic Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="business_name" class="block text-sm font-medium text-gray-700 mb-2">
                Business Name *
              </label>
              <input
                type="text"
                id="business_name"
                name="business_name"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Your business name"
              />
            </div>

            <div>
              <label for="display_name" class="block text-sm font-medium text-gray-700 mb-2">
                Display Name
              </label>
              <input
                type="text"
                id="display_name"
                name="display_name"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Shorter name for display (optional)"
              />
            </div>
          </div>

          <!-- City and Category -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="city_id" class="block text-sm font-medium text-gray-700 mb-2">
                City *
              </label>
              <select
                id="city_id"
                name="city_id"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a city</option>
                {cities?.map((cityOption) => (
                  <option
                    value={cityOption.id}
                    selected={city?.id === cityOption.id}
                  >
                    {cityOption.name}, {cityOption.country}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label for="category_primary_id" class="block text-sm font-medium text-gray-700 mb-2">
                Primary Category *
              </label>
              <select
                id="category_primary_id"
                name="category_primary_id"
                required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a category</option>
                {categories?.map((category) => (
                  <option value={category.id}>{category.name}</option>
                ))}
              </select>
            </div>
          </div>

          <!-- Descriptions -->
          <div>
            <label for="description_short" class="block text-sm font-medium text-gray-700 mb-2">
              Short Description *
            </label>
            <textarea
              id="description_short"
              name="description_short"
              required
              rows="2"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Brief description of your business (1-2 sentences)"
            ></textarea>
          </div>

          <div>
            <label for="description_long" class="block text-sm font-medium text-gray-700 mb-2">
              Detailed Description
            </label>
            <textarea
              id="description_long"
              name="description_long"
              rows="4"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Detailed description of your services, specialties, and what makes you unique"
            ></textarea>
          </div>

          <!-- Contact Information -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="+52 ************"
                />
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div class="mt-6">
              <label for="website_url" class="block text-sm font-medium text-gray-700 mb-2">
                Website URL
              </label>
              <input
                type="url"
                id="website_url"
                name="website_url"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://yourbusiness.com"
              />
            </div>

            <div class="mt-6">
              <label for="address_full" class="block text-sm font-medium text-gray-700 mb-2">
                Full Address
              </label>
              <textarea
                id="address_full"
                name="address_full"
                rows="2"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Street address, neighborhood, city, postal code"
              ></textarea>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="languages_spoken" class="block text-sm font-medium text-gray-700 mb-2">
                  Languages Spoken
                </label>
                <input
                  type="text"
                  id="languages_spoken"
                  name="languages_spoken"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="English, Spanish, French (comma separated)"
                />
              </div>

              <div>
                <label for="price_range" class="block text-sm font-medium text-gray-700 mb-2">
                  Price Range
                </label>
                <select
                  id="price_range"
                  name="price_range"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select price range</option>
                  <option value="$">$ - Budget-friendly</option>
                  <option value="$$">$$ - Moderate</option>
                  <option value="$$$">$$$ - Upscale</option>
                  <option value="$$$$">$$$$ - Luxury</option>
                </select>
              </div>
            </div>

            <!-- Checkboxes -->
            <div class="mt-6 space-y-3">
              <label class="flex items-center">
                <input type="checkbox" id="owner_is_expat" name="owner_is_expat" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span class="ml-2 text-sm text-gray-700">Business owner is an expat</span>
              </label>

              <label class="flex items-center">
                <input type="checkbox" id="pet_friendly" name="pet_friendly" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span class="ml-2 text-sm text-gray-700">Pet-friendly</span>
              </label>

              <label class="flex items-center">
                <input type="checkbox" id="kid_friendly" name="kid_friendly" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span class="ml-2 text-sm text-gray-700">Kid-friendly</span>
              </label>
            </div>

            <div class="mt-6">
              <label for="services_offered" class="block text-sm font-medium text-gray-700 mb-2">
                Services Offered
              </label>
              <textarea
                id="services_offered"
                name="services_offered"
                rows="2"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="List your main services (comma separated)"
              ></textarea>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="border-t border-gray-200 pt-6">
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-600">
                <p>By submitting, you agree to our <a href="/terms" class="text-blue-600 hover:text-blue-700">Terms of Service</a> and <a href="/privacy" class="text-blue-600 hover:text-blue-700">Privacy Policy</a>.</p>
              </div>
              <button
                type="submit"
                class="px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
              >
                Submit Business
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Verification Info -->
      <div class="mt-12 bg-blue-50 rounded-xl p-8">
        <div class="text-center">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Want to Get Verified?</h3>
          <p class="text-gray-700 mb-6">
            After your listing is approved, you can upgrade to a verified listing for just $25 USD.
            Verified businesses get a trust badge, priority placement, and the ability to upload custom photos.
          </p>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <div class="text-center">
              <div class="text-2xl mb-2">✅</div>
              <div class="text-sm font-medium">Verified Badge</div>
            </div>
            <div class="text-center">
              <div class="text-2xl mb-2">📸</div>
              <div class="text-sm font-medium">Custom Photos</div>
            </div>
            <div class="text-center">
              <div class="text-2xl mb-2">⭐</div>
              <div class="text-sm font-medium">Priority Placement</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script>
  document.getElementById('business-form')?.addEventListener('submit', async (e) => {
    e.preventDefault();

    const formData = new FormData(e.target as HTMLFormElement);
    const data: any = {};

    // Convert form data to object
    for (const [key, value] of formData.entries()) {
      if (key === 'languages_spoken' || key === 'services_offered') {
        data[key] = value.toString().split(',').map(s => s.trim()).filter(s => s);
      } else if (key === 'owner_is_expat' || key === 'pet_friendly' || key === 'kid_friendly') {
        data[key] = formData.has(key);
      } else {
        data[key] = value;
      }
    }

    try {
      const response = await fetch('/api/submit-business', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (result.success) {
        alert('Business submitted successfully! We\'ll review it and get back to you within 24 hours.');
        (e.target as HTMLFormElement).reset();
      } else {
        alert('Error submitting business: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error submitting business. Please try again.');
    }
  });
</script>
